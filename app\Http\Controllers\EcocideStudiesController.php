<?php

namespace App\Http\Controllers;

use App\Models\ecocide_studies;
use Illuminate\Http\Request;

class EcocideStudiesController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        //
        $ecocide_studies=ecocide_studies::OrderBy('id','DESC')->get();
        return view('backend.ecocide_studies.all_ecocide_studies',compact('ecocide_studies'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
         return view('backend.ecocide_studies.create_ecocide_studies');
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        // 
          $ecocide_studies = ecocide_studies::create([
            'title' => $request->title,
            'details' => $request->details,
            'image' => $request->image,
            'publish_date' => $request->publish_date,
           
        ]);

        return redirect()->route('ecocide_studies');
    }

    /**
     * Display the specified resource.
     */
    public function show(ecocide_studies $ecocide_studies)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(ecocide_studies $ecocide_studies,$id)
    {
        //
        $ecocide_studies=ecocide_studies::findorfail($id);
        return view('backend.ecocide_studies.edit_ecocide_studies',compact('ecocide_studies'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, ecocide_studies $ecocide_studies,$id)
    {
        //
        $ecoside_studies=ecocide_studies::findorfail($id);
          $ecoside_studies->update([
                    'title' => $request->title,
                    'publish_date'=>$request->publish_date,
                    'image' => $request->image,
                    'details' => $request->details
                ]);

                return redirect()->route('ecocide_studies');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(ecocide_studies $ecocide_studies,$id)
    {
        //
       ecocide_studies::findOrFail($id)->delete();
    
        return redirect()
        ->route('ecocide_studies');

    }
}
