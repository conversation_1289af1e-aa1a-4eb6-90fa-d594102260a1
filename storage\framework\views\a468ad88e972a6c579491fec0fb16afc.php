<?php $__env->startSection('main'); ?>
        <!-- Start app main Content -->
        <div class="main-content">
        <section class="section">
                

                <div class="section-body">
                    
                    <div class="row">
                        <div class="col-12 col-md-12 col-lg-12">
                            <div class="card">
                                <div class="card-header">
                                    <h4>Team</h4>
                                </div>
                                <div class="card-body">
                                    <div class="table-responsive">
                                        <table class="table table-bordered table-md v_center">
                                            <tr>
                                                <th>#</th>
                                                <th>Category Name</th>
                                                
                                                <th>Action</th>
                                            </tr>
                                            <?php $__currentLoopData = $team_category; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $team_category): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <tr>
                                                <td><?php echo e($loop->iteration); ?></td>
                                                <td><?php echo e($team_category->category_name); ?></td>
                                                
                                               
                                                
                                                <td><a href="<?php echo e(route('team_category.Edit', $team_category->id)); ?>" class="btn btn-secondary">Edit</a>
                                                <a href="<?php echo e(route('team_category.delete', $team_category->id)); ?>" onclick="return confirm('Are you sure you want to delete this post?');" class="btn btn-danger">Delete</a>
                                            </td>
                                            </tr>
                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                          
                                        </table> 
                                    </div>
                                </div>
                             
                            </div>
                        </div>
                        
                    </div>
                 
                    
                </div>
            </section>
        </div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('backend.master', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\laragon\www\interprt\resources\views/backend/team_category/all_team_category.blade.php ENDPATH**/ ?>