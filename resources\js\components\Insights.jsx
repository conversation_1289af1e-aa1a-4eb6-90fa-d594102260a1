import '@fancyapps/ui/dist/fancybox/fancybox.css';
import { Fancybox } from "@fancyapps/ui";
import React, { useEffect, useState, useRef } from "react";
import axios from 'axios';

function bindInsightCardClicks(startIndex = 0, count = 10) {
    setTimeout(() => {
        const teamCards = Array.from(document.querySelectorAll('.insight-wrap .single-insight'))
            .slice(startIndex, startIndex + count);

        teamCards.forEach(card => {
            const heading = card.querySelector('.insight-title-wrap h3');
            const content = card.querySelector('.insight-content');

            if (heading && content && !heading.classList.contains('click-bound')) {
                heading.classList.add('click-bound'); // Prevent duplicate listeners

                heading.addEventListener('click', () => {
                    const isActive = content.classList.contains('active');
                    const parent = heading.closest('.single-insight');

                    document.querySelectorAll('.insight-wrap .single-insight').forEach(el => {
                        el.classList.remove('active');
                    });

                    document.querySelectorAll('.insight-content').forEach(el => {
                        el.classList.remove('active');
                        // el.style.height = '0px';
                    });

                    if (!isActive) {
                        content.classList.add('active');
                        parent.classList.add('active');

                        // Scroll the insight into view (adjust offset if needed)
                        const yOffset = -80; // adjust based on your header height if needed
                        const y = parent.getBoundingClientRect().top + window.pageYOffset + yOffset;

                        window.scrollTo({ top: y, behavior: 'smooth' });
                    }
                });
            }
        });
    }, 2000); // Delay in milliseconds
}

function capitalizeWords(text) {
    return text
        .toLowerCase()
        .split(' ')
        .map(word => word.charAt(0).toUpperCase() + word.slice(1))
        .join(' ');
}
const Insights = ({ activeFilterItem, setActiveFilterItem }) => {
    const [insights, setInsights] = useState([]);
    const [publications, setPublications] = useState([]);
    const [currentPage, setCurrentPage] = useState(1);
    const [hasMorePages, setHasMorePages] = useState(true);
    const [isLoading, setIsLoading] = useState(false);
    const [newItemsLoaded, setNewItemsLoaded] = useState(false);
    const [openMoreIndex, setOpenMoreIndex] = useState(null);
    const insightListRef = useRef(null);
    const [stickyIndex, setStickyIndex] = useState(null);

    useEffect(() => {
        loadPublications();
    }, []);

    const loadPublications = () => {
        setIsLoading(true);
        axios.get(`/api/insights?page=1&filter=publications&items=1000`)
            .then(response => {
                const newData = response.data;

                setPublications(newData);
                setIsLoading(false);
            })
            .catch(error => {
                console.error('Error fetching insights:', error);
                setIsLoading(false);
            });
    };

    const loadInsights = (page = 1, loadMore = false) => {
        setIsLoading(true);
        const limit = loadMore ? 10 : 25; // Items per page
        const offset = (page - 1) * limit;

        axios.get(`/api/insights?offset=${offset}&limit=${limit}&filter=${activeFilterItem}&page=${page}`)
            .then(response => {
                const newData = response.data;
                if (page === 1) {
                    setInsights(newData.items);
                } else {
                    setInsights(prevInsights => [...prevInsights, ...newData.items]);
                    setNewItemsLoaded(true);
                }

                // Check if there are more pages by comparing the total count with our current position
                setHasMorePages(newData.total > (offset + newData.items.length));
                setCurrentPage(page);
                setIsLoading(false);
            })
            .catch(error => {
                console.error('Error fetching insights:', error);
                setIsLoading(false);
            });
    };

    const loadMore = () => {
        if (!isLoading && hasMorePages) {
            const nextPage = currentPage + 1;
            setCurrentPage(nextPage);
            loadInsights(nextPage, true);

            // Get current scroll position
            const currentScrollPosition = window.scrollY;

            // Scroll down 200px from current position with smooth behavior
            setTimeout(() => {
                window.scrollTo({
                    top: currentScrollPosition + 600,
                    behavior: 'smooth'
                });
            }, 500); // Small delay to allow new content to load
        }
    };

    useEffect(() => {
        loadInsights();
    }, [activeFilterItem]);

    useEffect(() => {
        if (newItemsLoaded && insightListRef.current) {
            // Get all newly added items
            const items = insightListRef.current.querySelectorAll('.single-insight');
            const startIndex = (currentPage - 1) * 10; // Assuming 10 items per page

            // Apply animation to new items only
            for (let i = startIndex; i < items.length; i++) {
                const item = items[i];
                item.classList.add('animated', 'fadeInUp');

                // Remove animation class after animation completes
                item.addEventListener('animationend', () => {
                    item.classList.remove('animated', 'fadeInUp');
                }, { once: true });
            }

            setNewItemsLoaded(false);
        }
    }, [insights, newItemsLoaded, currentPage]);

    useEffect(() => {
        setTimeout(() => {

            // Cursor-follow logic
            const serviceItems = document.querySelectorAll('.insight-wrap .single-insight');

            const followImageCursor = (event, serviceItem) => {
                const rect = serviceItem.getBoundingClientRect();
                const image = serviceItem.querySelector('.insight-image'); // Use class selector instead of children[1]
                if (!image) return; // Prevent error if image not found
                const imageWidth = image.offsetWidth;
                const imageHeight = image.offsetHeight;
                const x = event.clientX - rect.left - imageWidth / 2;
                const y = event.clientY - rect.top - imageHeight / 2;
                image.style.transform = `translate(${x}px, ${y}px)`;
            };

            serviceItems.forEach(item => {
                item.addEventListener('mousemove', (e) => followImageCursor(e, item));
            });
            bindInsightCardClicks(0, 30);
            // Clean up
            return () => {
                serviceItems.forEach(item => item.removeEventListener('mousemove', followImageCursor));
            };
        }, 2000);
    }, [currentPage]);

    useEffect(() => {
        if (newItemsLoaded && insightListRef.current) {
            const items = insightListRef.current.querySelectorAll('.single-insight');
            const startIndex = (currentPage - 1) * 10; // Assuming 10 items per page
            const count = 10;

            // Apply animation to new items only
            for (let i = startIndex; i < items.length; i++) {
                const item = items[i];
                item.classList.add('animated', 'fadeInUp');
                item.addEventListener('animationend', () => {
                    item.classList.remove('animated', 'fadeInUp');
                }, { once: true });
            }

            // 👇 Bind click handler to newly added items
            bindInsightCardClicks(startIndex, count);

            setNewItemsLoaded(false);
        }
    }, [insights, newItemsLoaded, currentPage]);

    // Disable arrow keys in the filter section
    useEffect(() => {
        const container = document.querySelector('.filter-insights');

        const handleArrowKeyBlock = (e) => {
            const arrowKeys = ['ArrowLeft', 'ArrowRight', 'ArrowUp', 'ArrowDown'];
            if (arrowKeys.includes(e.key)) {
                e.preventDefault();
            }
        };

        if (container) {
            container.addEventListener('keydown', handleArrowKeyBlock);
        }

        return () => {
            if (container) {
                container.removeEventListener('keydown', handleArrowKeyBlock);
            }
        };
    }, []);
    useEffect(() => {
        Fancybox.bind("[data-fancybox^='gallery-']");
        return () => {
            try {
                Fancybox.unbind("[data-fancybox^='gallery-']");
                Fancybox.close();
            } catch (e) {
                console.warn("Fancybox cleanup error:", e);
            }
        };
    }, [insights]);

    const handleFilterButtonClick = (filter) => {
        setActiveFilterItem(filter);
        document.querySelectorAll('.single-insight.active').forEach(function (el) {
            el.classList.remove('active');
        });
    };


    return (
        <div className="main-insights-wrapper home-page">
            <div className={`insight-sections${activeFilterItem === 'publications' ? ' active' : ''}`}>
                <div className="container">
                    <div className="row">
                        <div className="col-lg-12">
                            {/* Filter radio group START */}
                            <div className="filter-insights insight">
                                <div className="input-radio-wrapper">
                                    <ul>
                                        <li className={activeFilterItem === 'all' ? 'active' : ''}>
                                            <input
                                                type="radio"
                                                name="filter"
                                                id="all"
                                                value="all"
                                                onChange={() => handleFilterButtonClick('all')}
                                                checked={activeFilterItem === 'all'}
                                            />
                                            <label htmlFor="all">All projects</label>
                                        </li>
                                        <li className={activeFilterItem === 'investigations' ? 'active' : ''}>
                                            <input
                                                type="radio"
                                                name="filter"
                                                id="investigations"
                                                value="investigations"
                                                onChange={() => handleFilterButtonClick('investigations')}
                                                checked={activeFilterItem === 'investigations'}
                                            />
                                            <label htmlFor="investigations">Investigations</label>
                                        </li>
                                        <li className={activeFilterItem === 'exhibitions' ? 'active' : ''}>
                                            <input
                                                type="radio"
                                                name="filter"
                                                id="exhibitions"
                                                value="exhibitions"
                                                onChange={() => handleFilterButtonClick('exhibitions')}
                                                checked={activeFilterItem === 'exhibitions'}
                                            />
                                            <label htmlFor="exhibitions">Exhibitions</label>
                                        </li>
                                        <li className={activeFilterItem === 'events' ? 'active' : ''}>
                                            <input
                                                type="radio"
                                                name="filter"
                                                id="events"
                                                value="events"
                                                onChange={() => handleFilterButtonClick('events')}
                                                checked={activeFilterItem === 'events'}
                                            />
                                            <label htmlFor="events">Events</label>
                                        </li>
                                        <li className={activeFilterItem === 'publications' ? 'active' : ''}>
                                            <input
                                                type="radio"
                                                name="filter"
                                                id="publications"
                                                value="publications"
                                                onChange={() => handleFilterButtonClick('publications')}
                                                checked={activeFilterItem === 'publications'}
                                            />
                                            <label htmlFor="publications">Publications</label>
                                        </li>
                                    </ul>
                                </div>
                            </div>
                            {/* Filter radio group END */}
                        </div>
                    </div>
                </div>
                {activeFilterItem !== 'publications' && (
                    <ul className="insight-wrap" ref={insightListRef}>
                        {insights?.length > 0 && insights.map((item, idx) => (
                            <li key={idx} className={`single-insight`}>
                                <div className="container">
                                    <div className="row">
                                        <div className="col-12">
                                            <div className="insight-title-wrap">
                                                <h6>
                                                    <span> {item?.category?.category_name} </span>
                                                </h6>
                                                <h3>{capitalizeWords(item?.title)} </h3>
                                            </div>
                                            <div className="insight-image">
                                                {item?.image ? (
                                                    <img
                                                        src={item.image}
                                                        alt={item?.title}
                                                    />
                                                ) : Array.isArray(item?.image_gallery) && item.image_gallery.length > 0 ? (
                                                    <img
                                                        src={item.image_gallery[0]?.image}
                                                        alt={item?.title}
                                                    />
                                                ) : null}
                                            </div>
                                            <div className="insight-content">
                                                <p className="d-none">{item?.publish_date}</p>


                                                <div className="row g-4">

                                                    <div className="col-lg-9">
                                                        <div dangerouslySetInnerHTML={{ __html: item?.details }} />
                                                        <div className="gallary-image-wrap">
                                                            <div className="image-gallery">
                                                                <div className="row g-3">
                                                                    {item.image_gallery.slice(0, 6).map((value, Iidx) => (
                                                                        <div className="col-lg-3 col-md-4 col-6"  key={Iidx}>
                                                                            <a
                                                                                href={value?.image}
                                                                                data-fancybox={`gallery-${item?.id || idx}`}
                                                                                data-caption={item?.title || ''}
                                                                            >
                                                                                <img src={value?.image} alt="" />
                                                                            </a>
                                                                        </div>
                                                                    ))}
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div className="col-lg-3">
                                                        <div className="gallery-and-location">
                                                            {(item?.date || item?.venue || item?.Period) && (
                                                                <div className="media-coverage-item-ap">

                                                                    {item?.Period && (
                                                                        <div className="m-coverage-info-ap">{item.Period}</div>
                                                                    )}
                                                                    {item?.date && (
                                                                        <div className="m-coverage-info-ap">{item.date}</div>
                                                                    )}
                                                                    {item?.venue && (
                                                                        <div className="m-coverage-info-ap">{item.venue}</div>
                                                                    )}
                                                                </div>
                                                            )}
                                                        </div>
                                                        {hasMoreDetails(item) && (
                                                            <>
                                                                <div
                                                                    className="more-info-btn text-start"
                                                                    onClick={() => setOpenMoreIndex(openMoreIndex === idx ? null : idx)}
                                                                    style={{ cursor: 'pointer' }}
                                                                >
                                                                    More Information +
                                                                </div>
                                                                <div className={`team-block-in-ap more-info-ap${openMoreIndex === idx ? ' active' : ''}`}>
                                                                    <ul className="team-list-ap article-info-idp">
                                                                        {item?.book_title && (
                                                                            <li>
                                                                                <div className="media-coverage-item-ap">
                                                                                    <div className="m-coverage-title-ap">Book Title *</div>
                                                                                    <div className="m-coverage-info-ap">{item.book_title}</div>
                                                                                </div>
                                                                            </li>
                                                                        )}
                                                                        {item?.url && (
                                                                            <li>
                                                                                <div className="media-coverage-item-ap">
                                                                                    <div className="m-coverage-title-ap">URL *</div>
                                                                                    <div className="m-coverage-info-ap">{item.url}</div>
                                                                                </div>
                                                                            </li>
                                                                        )}
                                                                        {item?.situation && (
                                                                            <li>
                                                                                <div className="media-coverage-item-ap">
                                                                                    <div className="m-coverage-title-ap">Situation</div>
                                                                                    <div className="m-coverage-info-ap">{item.situation}</div>
                                                                                </div>
                                                                            </li>
                                                                        )}
                                                                        {item?.methodology && (
                                                                            <li>
                                                                                <div className="media-coverage-item-ap">
                                                                                    <div className="m-coverage-title-ap">Methodology</div>
                                                                                    <div className="m-coverage-info-ap">{item.methodology}</div>
                                                                                </div>
                                                                            </li>
                                                                        )}
                                                                        {item?.partners && (
                                                                            <li>
                                                                                <div className="media-coverage-item-ap">
                                                                                    <div className="m-coverage-title-ap">Partners</div>
                                                                                    <div className="m-coverage-info-ap">{item.partners}</div>
                                                                                </div>
                                                                            </li>
                                                                        )}
                                                                        {item?.types && (
                                                                            <li>
                                                                                <div className="media-coverage-item-ap">
                                                                                    <div className="m-coverage-title-ap">Type</div>
                                                                                    <div className="m-coverage-info-ap">{item.types}</div>
                                                                                </div>
                                                                            </li>
                                                                        )}
                                                                        {item?.project_team && (
                                                                            <li>
                                                                                <div className="media-coverage-item-ap">
                                                                                    <div className="m-coverage-title-ap">Project Team</div>
                                                                                    <div
                                                                                        className="m-coverage-info-ap"
                                                                                        dangerouslySetInnerHTML={{ __html: item.project_team }}
                                                                                    />
                                                                                </div>
                                                                            </li>
                                                                        )}
                                                                        {Array.isArray(item?.table_content) &&
                                                                            item.table_content.map((table, tIdx) =>
                                                                                table?.field_name && table?.value ? (
                                                                                    <li key={tIdx}>
                                                                                        <div className="media-coverage-item-ap">
                                                                                            <div className="m-coverage-title-ap">{table.field_name}</div>
                                                                                            <div
                                                                                                className="m-coverage-info-ap"
                                                                                                dangerouslySetInnerHTML={{ __html: table.value }}
                                                                                            ></div>
                                                                                        </div>
                                                                                    </li>
                                                                                ) : null
                                                                            )}
                                                                    </ul>
                                                                </div>
                                                            </>
                                                        )}

                                                    </div>

                                                </div>


                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </li>
                        ))}
                    </ul>
                )}

                {hasMorePages && activeFilterItem !== 'publications' && (
                    <div className="container">
                        <div className="row">
                            <div className="col-lg-12">
                                <div className="load-more-container mt-4">
                                    <button
                                        className="btn btn-primary load-more-btn"
                                        onClick={loadMore}
                                        disabled={isLoading}
                                    >
                                        {isLoading ? 'Loading...' : 'Load More'}
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                )}


            </div>
            <div className={`publication-section${activeFilterItem === 'publications' ? ' active' : ''}`}>
                <div className="container">
                    <div className="row">
                        <div className="col-lg-12">
                            <div className="section-title">
                                <h2>Publications</h2>
                            </div>
                            <div className="row g-4">

                                {publications?.length > 0 && publications.map((item, idx) => {
                                    if (!item?.url) return null;

                                    // Format publish_date
                                    const formattedDate = item.date
                                        ? new Date(item.date).toLocaleDateString('en-GB', {
                                            day: '2-digit',
                                            month: 'short',
                                            year: 'numeric'
                                        })
                                        : null;

                                    return (
                                        <div key={idx} className="col-lg-3 col-sm-6">
                                            <a className="single-publication-wrapper" href={item.url} target="_blank" rel="noopener noreferrer">
                                                {item.image && (
                                                    <img
                                                        src={item.image}
                                                        alt={item.title || "Publication Image"}
                                                    />
                                                )}
                                                {/* {item.date && <p>{item.date}</p>} */}
                                                {formattedDate && <p>{formattedDate}</p>}
                                                {item.book_title && <h3>{item.book_title}</h3>}
                                                {item.title && <h4>{item.title}</h4>}
                                            </a>
                                        </div>
                                    );
                                })}

                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );
};

function hasMoreDetails(item) {
    return !!(
        item?.book_title ||
        item?.url ||
        item?.situation ||
        item?.methodology ||
        item?.partners ||
        item?.types ||
        item?.project_team ||
        (Array.isArray(item?.table_content) && item.table_content.some(table => table?.field_name && table?.value))
    );
}

export default Insights;






