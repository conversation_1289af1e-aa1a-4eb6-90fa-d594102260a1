<?php $__env->startSection('main'); ?>
<!-- Start app main Content -->
<div class="main-content">
    <section class="section">
        <div class="section-body">
            <div class="row">
                <div class="col-12 col-md-12 col-lg-12">
                    <div class="card">
                        <div class="card-header" style="display: flex; justify-content: space-between; align-items: center;">
                            <div class="card-header-left">
                                <h4>Post</h4>
                                <div class="">
                                    <button id="publishTabBtn" class="btn btn-primary mr-2" type="button">Publish</button>
                                    <button id="draftTabBtn" class="btn btn-outline-primary" type="button">Draft</button>
                                </div>
                            </div>
                            <div class="card-header-right">
                                <form method="GET" action="<?php echo e(route('posts')); ?>" id="searchForm">
                                    <div class="input-group">
                                        <input type="text" class="form-control" name="search"
                                            placeholder="Search posts by title, content, or category..."
                                            value="<?php echo e($search ?? ''); ?>">
                                        <div class="input-group-append">
                                            <button class="btn btn-primary" type="submit">
                                                <i class="fas fa-search"></i> Search
                                            </button>
                                            <?php if($search): ?>
                                                <a href="<?php echo e(route('posts')); ?>" class="btn btn-secondary">
                                                    <i class="fas fa-times"></i> Clear
                                                </a>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                </form>
                            </div>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table id="publishTable" class="table table-bordered table-md v_center">
                                    <tr>
                                        <th>#</th>
                                        <th>Title</th>
                                        <th>Type</th>
                                        <th>Action</th>
                                    </tr>
                                    <?php $__currentLoopData = $posts; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key=> $post): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <tr>
                                        <td><?php echo e($loop->iteration); ?></td>
                                        <td><?php echo e($post->title); ?></td>
                                        <td><?php echo e($post->category->category_name ?? 'No Category'); ?></td>
                                        <td>
                                            <a href="<?php echo e(route('posts.edit', $post->id)); ?>" class="btn btn-secondary">Edit</a>
                                            <a href="<?php echo e(route('posts.delete', $post->id)); ?>" class="btn btn-danger" onclick="return confirm('Are you sure you want to delete this post?');">Delete</a>
                                        </td>
                                    </tr>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </table>

                                <table id="draftTable" class="table table-bordered table-md v_center" style="display:none;">
                                    <tr>
                                        <th>#</th>
                                        <th>Image</th>
                                        <th>Title</th>
                                        <th>Type</th>
                                        <th>Details</th>
                                        <th>Other Information</th>
                                        <th>Action</th>
                                    </tr>
                                    <?php $__currentLoopData = $drafts; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key=> $post): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <tr>
                                        <td><?php echo e($loop->iteration); ?></td>
                                        <td>
                                            <?php if($post->image): ?>
                                            <img src="<?php echo e($post->image); ?>" alt="<?php echo e($post->title); ?>" style="max-width: 100px; max-height: 80px;">
                                            <?php else: ?>
                                            <span class="text-muted">No Image</span>
                                            <?php endif; ?>
                                        </td>
                                        <td><?php echo e($post->title); ?></td>
                                        <td><?php echo e($post->category->category_name ?? 'No Category'); ?></td>
                                        <td><?php echo $post->details; ?></td>
                                        <td>
                                            <?php $__currentLoopData = $post->table_content; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $c_table): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                <span style="padding-right:50px"><b><?php echo e($c_table->field_name); ?></b></span>
                                                <?php echo $c_table->value; ?><br>
                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                        </td>
                                        <td>
                                            <a href="<?php echo e(route('posts.edit', $post->id)); ?>" class="btn btn-secondary">Edit</a>
                                            <a href="<?php echo e(route('posts.delete', $post->id)); ?>" class="btn btn-danger" onclick="return confirm('Are you sure you want to delete this post?');">Delete</a>
                                        </td>
                                    </tr>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </table>

                                <!-- Published Posts Results Info and Pagination -->
                                <div id="publishPagination">
                                    <?php if($search): ?>
                                        <div class="mb-2">
                                            <small class="text-muted">
                                                Found <?php echo e($posts->total()); ?> published post(s) matching your search.
                                            </small>
                                        </div>
                                    <?php endif; ?>
                                    <?php echo e($posts->links()); ?>

                                </div>

                                <!-- Draft Posts Results Info and Pagination -->
                                <div id="draftPagination" style="display:none;">
                                    <?php if($search): ?>
                                        <div class="mb-2">
                                            <small class="text-muted">
                                                Found <?php echo e($drafts->total()); ?> draft post(s) matching your search.
                                            </small>
                                        </div>
                                    <?php endif; ?>
                                    <?php echo e($drafts->links()); ?>

                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const publishBtn = document.getElementById('publishTabBtn');
    const draftBtn = document.getElementById('draftTabBtn');
    const publishTable = document.getElementById('publishTable');
    const draftTable = document.getElementById('draftTable');
    const publishPagination = document.getElementById('publishPagination');
    const draftPagination = document.getElementById('draftPagination');

    publishBtn.addEventListener('click', function() {
        publishTable.style.display = '';
        draftTable.style.display = 'none';
        publishPagination.style.display = '';
        draftPagination.style.display = 'none';
        publishBtn.classList.add('btn-primary');
        publishBtn.classList.remove('btn-outline-primary');
        draftBtn.classList.remove('btn-primary');
        draftBtn.classList.add('btn-outline-primary');
    });

    draftBtn.addEventListener('click', function() {
        publishTable.style.display = 'none';
        draftTable.style.display = '';
        publishPagination.style.display = 'none';
        draftPagination.style.display = '';
        draftBtn.classList.add('btn-primary');
        draftBtn.classList.remove('btn-outline-primary');
        publishBtn.classList.remove('btn-primary');
        publishBtn.classList.add('btn-outline-primary');
    });
});
</script>
<?php $__env->stopSection(); ?>
<?php echo $__env->make('backend.master', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\laragon\www\interprt\resources\views/backend/posts/all_post.blade.php ENDPATH**/ ?>