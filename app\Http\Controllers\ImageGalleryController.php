<?php

namespace App\Http\Controllers;

use App\Models\image_gallery;
use Illuminate\Http\Request;

class ImageGalleryController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        //
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        //
    }

    /**
     * Display the specified resource.
     */
    public function show(image_gallery $image_gallery)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(image_gallery $image_gallery)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, image_gallery $image_gallery)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(image_gallery $image_gallery)
    {
        //
    }
}
