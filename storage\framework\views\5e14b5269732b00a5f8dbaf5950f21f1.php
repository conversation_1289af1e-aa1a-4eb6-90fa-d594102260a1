<?php $__env->startSection('main'); ?>
        <!-- Start app main Content -->
        <div class="main-content">
        <section class="section">
                

                <div class="section-body">
                    
                    <div class="row">
                        <div class="col-12 col-md-12 col-lg-12">
                            <div class="card">
                                <div class="card-header">
                                    <h4>Post</h4>
                                </div>
                                <div class="card-body">
                                    <div class="table-responsive">
                                        <table class="table table-bordered table-md v_center">
                                            <tr>
                                                <th>#</th>
                                                <th>Image</th>
                                                <th>Title</th>
                                                <th>Type</th>
                                                <th>Details</th>
                                                <th>Other Information</th>
                                                <th>Action</th>
                                            </tr>
                                            <?php $__currentLoopData = $posts; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key=> $post): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <tr>
                                                <td><?php echo e($loop->iteration); ?></td>
                                                <td>
                                                    <?php if($post->image): ?>
                                                        <img src="<?php echo e($post->image); ?>" alt="<?php echo e($post->title); ?>" style="max-width: 100px; max-height: 80px;">
                                                    <?php else: ?>
                                                        <span class="text-muted">No Image</span>
                                                    <?php endif; ?>
                                                </td>
                                                <td><?php echo e($post->title); ?></td>
                                               <td><?php echo e($post->category->category_name ?? 'No Category'); ?></td>
                                                <td><?php echo $post->details; ?></td>
                                              <td><?php $__currentLoopData = $post->table_content; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $c_table): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                 <span style="padding-right:50px"> <b>  <?php echo e($c_table->field_name); ?></b></span>
                                                    <?php echo $c_table->value; ?><br>
                                              <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                              </td>
                                                <td><a href="<?php echo e(route('posts.edit', $post->id)); ?>" class="btn btn-secondary">Edit</a>
                                                <a href="<?php echo e(route('posts.delete', $post->id)); ?>" class="btn btn-danger" onclick="return confirm('Are you sure you want to delete this post?');">Delete</a>
                                            </td>
                                            </tr>
                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                          
                                        </table> 
                                        <!-- Pagination links -->
                                                <?php echo e($posts->links()); ?>

                                    </div>
                                </div>
                                <!-- <div class="card-footer text-right">
                                    <nav class="d-inline-block">
                                        <ul class="pagination mb-0">
                                            <li class="page-item disabled"><a class="page-link" href="#" tabindex="-1"><i class="fas fa-chevron-left"></i></a></li>
                                            <li class="page-item active"><a class="page-link" href="#">1 <span class="sr-only">(current)</span></a></li>
                                            <li class="page-item"><a class="page-link" href="#">2</a></li>
                                            <li class="page-item"><a class="page-link" href="#">3</a></li>
                                            <li class="page-item"><a class="page-link" href="#"><i class="fas fa-chevron-right"></i></a></li>
                                        </ul>
                                    </nav>
                                </div> -->
                            </div>
                        </div>
                        
                    </div>
                 
                    
                </div>
            </section>
        </div>
<?php $__env->stopSection(); ?>
<?php echo $__env->make('backend.master', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\laragon\www\interprt\resources\views/backend/posts/filter_post.blade.php ENDPATH**/ ?>