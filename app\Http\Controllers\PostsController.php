<?php

namespace App\Http\Controllers;
use App\Models\table_content;
use App\Models\posts;
use App\Models\category;
use App\Models\image_gallery;
use Illuminate\Http\Request;

class PostsController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        //
            $posts = posts::with('category','table_content','image_gallery')->whereNull('draft')->orderBy('id','DESC')->paginate(10);
            // dd($posts);
            $drafts=posts::with('category','table_content')->where('draft','on')->orderBy('id','DESC')->paginate(10);
        
        // dd($posts);
        return view('backend.posts.all_post',compact('posts' ,'drafts'));
    }


     public function filterCategory($id)
    {
        //
            $posts = posts::where('type',$id)->with('category','table_content')->orderBy('id','DESC')->paginate(10);
        
        // dd($posts);
        return view('backend.posts.filter_post',compact('posts'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
        $category=category::orderBy('id','ASC')->get();
        return view('backend.posts.add_post',compact('category'));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
{
 
   
   $type=$request->type;

    if($type==3){

            $post = posts::create([
            'title' => $request->title,
            'type' => $request->type,
            'details' => $request->details,
            'image' => $request->image,
            'publish_date' => $request->publish_date,
            'situation'=>$request->situation,
            'Period'=>$request->Period,
            'venue'=>$request->venue,
            'methodology'=>$request->methodology,
            'partners'=>$request->partners,
            'project_team'=>$request->project_team,
            'draft'=>$request->draft
        ]);
    }

        if($type==4){
       
       
            $post = posts::create([
            'title' => $request->title,
            'type' => $request->type,
            'details' => $request->details,
            'image' => $request->image,
            'publish_date' => $request->publish_date,
            'venue'=>$request->venue_one,
            'date'=>$request->date,
            'partners'=>$request->partner_one,
            'types'=>$request->type_one,
            'draft'=>$request->draft
            
        ]);
    }


     if($type==5){
       
       
            $post = posts::create([
            'title' => $request->title,
            'type' => $request->type,
            'details' => $request->details,
            'image' => $request->image,
            'publish_date' => $request->publish_date,
            'venue'=>$request->venue_two,
            'date'=>$request->date_one,
            'partners'=>$request->partner_two,
            'types'=>$request->type_two,
            'draft'=>$request->draft
            
        ]);
    }

        if($type==6){

      
            $post = posts::create([
            'title' => $request->title,
            'type' => $request->type,
            'details' => $request->details,
            'image' => $request->image,
            'publish_date' => $request->publish_date,
            'book_title'=>$request->book_title,
            'date'=>$request->date_three,
            'url'=>$request->url,
            'draft'=>$request->draft
          
        ]);
    }



     if($type=='Select Category'){

      
            $post = posts::create([
            'title' => $request->title,
            'type' => $request->type,
            'details' => $request->details,
            'image' => $request->image,
            'publish_date' => $request->publish_date
        ]);
    }


   
    
    // Save the post
   

    $post_id = $post->id; // get the correct ID from the inserted post


    // image gallery upload
        $imageGallery = $request->input('image_gallery'); // this will be an array

    if (!empty($imageGallery)) {
        foreach ($imageGallery as $image) {
            if (!empty($image)) {
                // Assuming you have an image_gallery model to handle the images
              
                image_gallery::create([
                  
                    'image' => $image,
                    'post_id' => $post_id, // use the ID of the newly created post
                ]);
            }
        }
    }

    $field_names = $request->input('field_name'); // array
$values = $request->input('value');           // array
if(!empty($field_names || $values)){
foreach ($field_names as $index => $field_name) {
    $field_value = $values[$index];

    // Skip if either field name or value is empty
    if (empty($field_name) || empty($field_value)) {
        continue;
    }

    table_content::create([
        'field_name' => $field_name,
        'value' => $field_value,
        'post_id' => $post_id,
    ]);
}
}

    return redirect()->route('posts');
}
    /**
     * Display the specified resource.
     */ 
    public function show(posts $posts)
    {
        //
    }

     public function test(posts $posts,$id)
    {
        //
        
        $post = posts::with('category','table_content')->findOrFail($id);
        $category=category::orderBy('id','ASC')->get();
        return view('backend.posts.test', compact('post','category'));
        
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(posts $posts,$id)
    {
        //
        
        $post = posts::with('category','table_content','image_gallery')->findOrFail($id);
        $category=category::orderBy('id','ASC')->get();
        return view('backend.posts.edit_post', compact('post','category'));
        
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, posts $posts,$id)
      {
        $post = posts::findOrFail($id);

        // Update post main data
      




        $type=$request->type;

        if($type==3){
              $post->update([
                    'title' => $request->title,
                    'publish_date'=>$request->publish_date,
                    'type' => $request->type,
                    'image' => $request->image,
                    'details' => $request->details,
                    'publish_date' => $request->publish_date,

                    'situation'=>$request->situation,
                    'Period'=>$request->Period,
                    'venue'=>$request->venue,
                    'methodology'=>$request->methodology,
                    'partners'=>$request->partners,
                    'project_team'=>$request->project_team,
                    'draft'=>$request->draft
                  

                ]);
        }
         if($type==4){
             $post->update([
                    'title' => $request->title,
                    'publish_date'=>$request->publish_date,
                    'type' => $request->type,
                    'image' => $request->image,
                    'details' => $request->details,
                    'publish_date' => $request->publish_date,
                    'venue'=>$request->venue_one,
                    'date'=>$request->date,
                    'partners'=>$request->partner_one,
                    'types'=>$request->type_one,
                    'draft'=>$request->draft                

                ]);
        }
         if($type==5){
            $post->update([
                    'title' => $request->title,
                    'publish_date'=>$request->publish_date,
                    'type' => $request->type,
                    'image' => $request->image,
                    'details' => $request->details,
                    'publish_date' => $request->publish_date,
                    'venue'=>$request->venue_two,
                    'date'=>$request->date_one,
                    'partners'=>$request->partner_two,
                    'types'=>$request->type_two,
                    'draft'=>$request->draft
                  

                ]);
        }
         if($type==6){
              $post->update([
                    'title' => $request->title,
                    'publish_date'=>$request->publish_date,
                    'type' => $request->type,
                    'image' => $request->image,
                    'details' => $request->details,
                    'publish_date' => $request->publish_date,
                    'book_title'=>$request->book_title,
                    'date'=>$request->date_three,
                    'partners'=>$request->partner_two,
                    'url'=>$request->url,
                    'draft'=>$request->draft
                  

                ]);
        }


         $post->image_gallery()->delete();

         // image gallery upload
        $imageGallery = $request->input('image_gallery'); // this will be an array

    if (!empty($imageGallery)) {
        foreach ($imageGallery as $image) {
            if (!empty($image)) {
                // Assuming you have an image_gallery model to handle the images
                
                image_gallery::create([
                  
                    'image' => $image,
                    'post_id' => $id, // use the ID of the newly created post
                ]);
            }
        }
    }

        
          

        // Delete old table_content records
        $post->table_content()->delete();

        // Re-insert new table_content
        $field_names = $request->input('field_name', []);
        $values = $request->input('value', []);

        foreach ($field_names as $index => $field_name) {
            $field_value = $values[$index] ?? null;

            if (empty($field_name) || empty($field_value)) {
                continue;
            }

            $post->table_content()->create([
                'field_name' => $field_name,
                'value' => $field_value,
            ]);
        }

        return redirect()->route('posts')->with('success', 'Post updated successfully.');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(posts $posts,$id)
    {
        //
        posts::findOrFail($id)->delete();
        table_content::where('post_id',$id)->delete();
        return redirect()
        ->route('posts');
    }
}
