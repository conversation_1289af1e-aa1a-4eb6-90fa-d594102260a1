import React, { useEffect, useState } from 'react';
import axios from 'axios';

const About = () => {
    const [aboutContent, setAboutContent] = useState('');
    const [teams, setTeams] = useState([]);
    const [pastTeams, setPastTeams] = useState([]);
    const [loading, setLoading] = useState(true);
    const [activeTeamItem, setActiveTeamItem] = useState(null);
    const [activeItemHeight, setActiveItemHeight] = useState("");

    useEffect(() => {
        // Fetch about content from Laravel backend
        axios.get('/api/about')
            .then(response => {
                setAboutContent(response.data.details || '');
                setLoading(false);
            })
            .catch(error => {
                console.error('Error fetching about content:', error);
                setLoading(false);
            });
        // Fetch teams from Laravel backend
        axios.get('/api/teams')
            .then(response => {
                setTeams(response.data);
                setLoading(false);
            })
            .catch(error => {
                console.error('Error fetching teams:', error);
                setLoading(false);
            });
        // Fetch past teams from Laravel backend
        axios.get('/api/past_teams')
            .then(response => {
                setPastTeams(response.data);
                setLoading(false);
            })
            .catch(error => {
                console.error('Error fetching past teams:', error);
                setLoading(false);
            });
    }, []);

    useEffect(() => {
        if (activeTeamItem) {
            const activeTeam = document.querySelector('.insight-content.active .team-content-wrapper').getBoundingClientRect();
            setActiveItemHeight(activeTeam?.height + 15);
        }
    }, [activeTeamItem])

    return (
        <div className="about-page-wrapper">
            <div className="about-sections">
                <div className="container">
                <div className="row">
                    <div className="col-lg-12">
                        <div className="about-content">
                            {loading ? (
                                <div className="loading-spinner"></div>
                            ) : (
                                <div
                                    className="about-text"
                                    dangerouslySetInnerHTML={{ __html: aboutContent }}
                                />
                            )}
                        </div>
                    </div>
                </div>
            </div>
            </div>
            <div className="team-sections">
                <div className="main-insights-wrapper teams">
                    <div className="insight-sections">
                        <div className="container">
                            <div className="row">
                                <div className="col-12">
                                    <div className="section-title">
                                        <h2>Teams</h2>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <ul className="insight-wrap">
                            {teams?.length > 0 && teams.map((item, idx) => (
                                <li key={idx} className="single-insight active">
                                    <div className="container">
                                        <div className="row">
                                            <div className="col-12">
                                                <div className="insight-title-wrap">
                                                    <h6>
                                                        <span> {item?.team_category?.category_name} </span>
                                                    </h6>
                                                    <h3 onClick={() => {
                                                        if (null == activeTeamItem) {
                                                            setActiveTeamItem(item?.id);
                                                        } else if (activeTeamItem == item?.id) {
                                                            setActiveTeamItem(null);
                                                        } else {
                                                            setActiveTeamItem(item?.id);
                                                        }
                                                    }}>{item?.name}</h3>
                                                </div>
                                                <div className="insight-content active">
                                                    <div className='team-content-wrapper'>
                                                        <p className="d-none">{item?.publish_date}</p>
                                                        <div className="mt-15" dangerouslySetInnerHTML={{ __html: item?.details }} />
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </li>
                            ))}
                        </ul>
                    </div>
                   
                </div>
            </div>
            <div className="past-team-sections">
                <div className="main-insights-wrapper past-teams">
                    <div className="insight-sections">
                        <div className="container">
                            <div className="row">
                                <div className="col-12">
                                    <div className="section-title">
                                        <h2>Past Teams</h2>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <ul className="insight-wrap">
                            {pastTeams?.length > 0 && pastTeams.map((item, idx) => (
                                <li key={idx} className={`single-insight ${activeTeamItem === item?.id ? 'active' : ''}`}>
                                    <div className="container">
                                        <div className="row">
                                            <div className="col-12">
                                                <div className="insight-title-wrap">
                                                    <h6>
                                                        <span> {item?.team_category?.category_name} </span>
                                                    </h6>
                                                    <h3 onClick={() => {
                                                        if (null == activeTeamItem) {
                                                            setActiveTeamItem(item?.id);
                                                        } else if (activeTeamItem == item?.id) {
                                                            setActiveTeamItem(null);
                                                        } else {
                                                            setActiveTeamItem(item?.id);
                                                        }
                                                    }}>{item?.name}</h3>
                                                </div>
                                                <div style={{ height: `${activeTeamItem == item?.id ? activeItemHeight : ''}` }} className={`insight-content ${activeTeamItem === item?.id ? 'active' : ''}`}>
                                                    <div className='team-content-wrapper'>
                                                        <p className="d-none">{item?.publish_date}</p>
                                                        <div className="mt-15" dangerouslySetInnerHTML={{ __html: item?.details }} />
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </li>
                            ))}
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    );
};

export default About;