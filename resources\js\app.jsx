import React, { useEffect, useState } from 'react';
import <PERSON>actD<PERSON> from 'react-dom/client';
import { BrowserRouter, Routes, Route } from 'react-router-dom';
import Header from './components/Header';
import Insights from './components/Insights';
import About from './components/About';
import Ecocide from './components/Ecocide';
import Footer from './components/Footer'; // Add this import

const App = () => {
    const [activeFilterItem, setActiveFilterItem] = useState('all');

    useEffect(() => {
        setTimeout(() => {
            // Hamburger click handler
            const toggleHamburger = () => {
                document.querySelectorAll('.comp-header_main_header_container__fB_W0').forEach(header => {
                    header.classList.toggle('comp-header_active__nxkGN');
                });

                document.querySelectorAll('.header-flyout-menu_flyout_menu_container__tcC_d').forEach(menu => {
                    menu.classList.toggle('header-flyout-menu_is_active__NQ4_G');
                });

                document.querySelectorAll('.comp-header_theme_selector_container__z3mks').forEach(menu => {
                    menu.classList.toggle('comp-header_active__nxkGN');
                });

                document.querySelectorAll('.header-hamburger_burger_holder__tOCQw').forEach(hamburger => {
                    hamburger.classList.toggle('header-hamburger_is_active__x3sIj');
                });
            };

            const burgers = document.querySelectorAll('.header-hamburger_burger_holder__tOCQw');
            burgers.forEach(b => b.addEventListener('click', toggleHamburger));

            // Clean up
            return () => {
                burgers.forEach(b => b.removeEventListener('click', toggleHamburger));
            };
        }, 1000);
    }, []);

    return (
        <BrowserRouter>
            <Header
                activeFilterItem={activeFilterItem}
                setActiveFilterItem={setActiveFilterItem}
            />
            <Routes>
                <Route path="/" element={
                    <Insights
                        activeFilterItem={activeFilterItem}
                        setActiveFilterItem={setActiveFilterItem}
                    />
                } />
                <Route path="/about" element={<About />} />
                <Route path="/ecocide" element={<Ecocide />} />
            </Routes>
            <Footer /> {/* Add Footer here */}
        </BrowserRouter>
    );
};

const root = ReactDOM.createRoot(document.getElementById('react-root'));
root.render(<App />);

