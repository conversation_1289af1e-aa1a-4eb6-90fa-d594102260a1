<?php $__env->startSection('main'); ?>

<!-- Start app main Content -->
<div class="main-content">
    <section class="section">

        <div class="section-body">



            <div class="row">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">

                            <h4>Add Post</h4>
                        </div>
                        <div class="card-body">
                            <form action="<?php echo e(route('posts.update', $post->id)); ?>" method="POST">
                                <?php echo csrf_field(); ?>
                                <?php echo method_field('PUT'); ?>
                                <div class="form-group row mb-4">
                                    <label class="col-form-label text-md-right col-12 col-md-3 col-lg-3">Title</label>
                                    <div class="col-sm-12 col-md-7">

                                        <input type="text" name="title" class="form-control" value="<?php echo e($post->title); ?>">
                                    </div>
                                </div>

                                <div class="form-group row mb-4">
                                    <label class="col-form-label text-md-right col-12 col-md-3 col-lg-3">Publish Date</label>
                                    <div class="col-sm-12 col-md-7">
                                        <input type="date" name="publish_date" class="form-control" value="<?php echo e($post->publish_date); ?>">
                                    </div>
                                </div>


                                <div class="form-group row mb-4">
                                    <label class="col-form-label text-md-right col-12 col-md-3 col-lg-3">Category</label>
                                    <div class="col-sm-12 col-md-7">
                                        <select class="form-control selectric" name="type">
                                            <option value="<?php echo e($post->type); ?>"><?php echo e($post->category->category_name); ?></option>
                                            <?php $__currentLoopData = $category; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $category): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <option value="<?php echo e($category->id); ?>"><?php echo e($category->category_name); ?></option>
                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>

                                        </select>
                                    </div>
                                </div>

                               



                                        <div class="form-group row mb-4">
                                        <label class="col-form-label text-md-right col-12 col-md-3 col-lg-3">Featured Image</label>
                                        <div class="col-sm-12 col-md-7">
                                            <div class="input-group">
                                                <span class="input-group-btn">
                                                    <a id="lfm" data-input="thumbnail" data-preview="holder" class="btn btn-primary">
                                                        <i class="fa fa-picture-o"></i> Choose
                                                    </a>
                                                </span>
                                                <input id="thumbnail" class="form-control" type="text" name="image">
                                            </div>
                                            <div id="holder" style="margin-top:15px;max-height:200px;">
                                            <?php if($post->image): ?>
                                            <img src="<?php echo e($post->image); ?>" style="height: 5rem;">
                                            <?php endif; ?>
                                        </div>
                                          
                               
                                        </div>
                                    </div>

                                    
 
                                <div class="form-group row mb-4">
                                    <label class="col-form-label text-md-right col-12 col-md-3 col-lg-3">Image Gallery</label>
                                    <div class="col-sm-12 col-md-7">
                                        <div id="gallery-wrapper">
                                            <?php $__currentLoopData = $post->image_gallery; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $gallery): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <div class="image-group mb-3" data-index="<?php echo e($index); ?>">
                                                <div class="input-group">
                                                    <span class="input-group-btn">
                                                        <a id="lfm_<?php echo e($index); ?>" class="btn btn-primary lfm-btn" data-input="image_<?php echo e($index); ?>" data-preview="holder_<?php echo e($index); ?>">
                                                            <i class="fa fa-picture-o"></i> Choose
                                                        </a>
                                                    </span>
                                                    <input id="image_<?php echo e($index); ?>" class="form-control" type="text" name="image_gallery[]" value="<?php echo e($gallery->image); ?>">
                                                </div>
                                                <div id="holder_<?php echo e($index); ?>" style="margin-top:15px; max-height:200px;">
                                                    <?php if($gallery->image): ?>
                                                    <img src="<?php echo e($gallery->image); ?>" style="height: 5rem;">
                                                    <?php endif; ?>
                                                </div>
                                                <button type="button" class="btn btn-danger btn-sm mt-2 btn-remove">Remove</button>
                                            </div>
                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                        </div>
                                        <button type="button" class="btn btn-success btn-sm mt-2" id="add-photo">+ Add Photo</button>
                                    </div>
                                </div>
                        


                                <div class="form-group row mb-4">
                                    <label class="col-form-label text-md-right col-12 col-md-3 col-lg-3">Details</label>
                                    <div class="col-sm-12 col-md-7">
                                        <textarea class="summernote" name="details"><?php echo e(old('details', $post->details)); ?></textarea>
                                        <!-- <textarea class="summernote" name="details"><?php echo e($post->details); ?></textarea> -->
                                    </div>
                                </div>



                                <div class="form-group row mb-4">
                                    <label class="col-form-label text-md-right col-12 col-md-3 col-lg-3"></label>
                                    <div class="col-sm-12 col-md-7">
                                        <div class="input-group">


                                            <!-- category fields -->
                                            <div id="dynamic-fields-container" style="width: 100%;">
                                                <div class="field-group type-3 d-none">
                                                    <!-- Type 3 Fields -->
                                                    <div class="form-group mb-2">
                                                        <label class="form-label">Situation</label>
                                                        <input type="text" name="situation" class="form-control" placeholder="Enter Situation" value="<?php echo e($post->situation); ?>">
                                                    </div>

                                                    <div class="form-group mb-2">
                                                        <label class="form-label">Period</label>
                                                        <input type="text" name="Period" class="form-control" placeholder="Enter Period" value="<?php echo e($post->Period); ?>">
                                                    </div>



                                                    <div class="form-group mb-2">
                                                        <label class="form-label">Venue</label>
                                                        <input type="text" name="venue" class="form-control" placeholder="Enter Venue" value="<?php echo e($post->venue); ?>">
                                                    </div>

                                                    <div class="form-group mb-2">
                                                        <label class="form-label">Methodology</label>
                                                        <input type="text" name="methodology" class="form-control" placeholder="Enter Methodology" value="<?php echo e($post->methodology); ?>">
                                                    </div>

                                                    <div class="form-group mb-2">
                                                        <label class="form-label">Partners</label>
                                                        <input type="text" name="partners" class="form-control" placeholder="Enter Partners" value="<?php echo e($post->partners); ?>">
                                                    </div>

                                                    <!-- Repeat for all fields -->
                                                    <div class="form-group mb-2">
                                                        <label class="form-label">Project Team</label>
                                                        <textarea name="project_team" class="form-control summernote"><?php echo $post->project_team; ?></textarea>
                                                    </div>
                                                </div>

                                                <div class="field-group type-4 d-none">
                                                    <div class="form-group mb-2">
                                                        <label class="form-label">Location </label>
                                                        <input type="text" name="venue_one" class="form-control" placeholder="Enter Location" value="<?php echo e($post->venue); ?>">
                                                    </div>

                                                    <div class="form-group mb-2">
                                                        <label class="form-label">Date </label>
                                                        <input type="text" name="date" class="form-control" placeholder="Enter Date" value="<?php echo e($post->date); ?>">
                                                    </div>

                                                    <div class="form-group mb-2">
                                                        <label class="form-label">PARTNERS </label>
                                                        <input type="text" name="partner_one" class="form-control" placeholder="Enter Partners" value="<?php echo e($post->partners); ?>">
                                                    </div>

                                                    <div class="form-group mb-2">
                                                        <label class="form-label">Type </label>
                                                        <input type="text" name="type_one" class="form-control" placeholder="Enter Type" value="<?php echo e($post->types); ?>">
                                                    </div>
                                                    <!-- Add other fields -->
                                                </div>

                                                <div class="field-group type-5 d-none">
                                                    <!-- Type 5 Fields, similar to type 4 -->
                                                    <div class="form-group mb-2">
                                                        <label class="form-label">Location </label>
                                                        <input type="text" name="venue_two" class="form-control" placeholder="Enter Location" value="<?php echo e($post->venue); ?>">
                                                    </div>

                                                    <div class="form-group mb-2">
                                                        <label class="form-label">Date </label>
                                                        <input type="text" name="date_one" class="form-control" placeholder="Enter Date" value="<?php echo e($post->date); ?>">
                                                    </div>

                                                    <div class="form-group mb-2">
                                                        <label class="form-label">PARTNERS </label>
                                                        <input type="text" name="partner_two" class="form-control" placeholder="Enter Partners " value="<?php echo e($post->partners); ?>">
                                                    </div>

                                                    <div class="form-group mb-2">
                                                        <label class="form-label">Type </label>
                                                        <input type="text" name="type_two" class="form-control" placeholder="Enter Type" value="<?php echo e($post->types); ?>">
                                                    </div>
                                                    <!-- Add other fields -->
                                                </div>

                                                <div class="field-group type-6 d-none">
                                                    <!-- Type 6 Fields -->
                                                    <div class="form-group mb-2">
                                                        <label class="form-label">Book Title </label>
                                                        <input type="text" name="book_title" class="form-control" placeholder="Enter Book Title" value="<?php echo e($post->book_title); ?>">
                                                    </div>

                                                    <div class="form-group mb-2">
                                                        <label class="form-label">Date </label>
                                                        <input type="text" name="date_three" class="form-control" placeholder="Enter Date" value="<?php echo e($post->date); ?>">
                                                    </div>

                                                    <div class="form-group mb-2">
                                                        <label class="form-label">URL </label>
                                                        <input type="text" name="url" class="form-control" placeholder="Enter URL" value="<?php echo e($post->url); ?>">
                                                    </div>
                                                    <!-- Add other fields -->
                                                </div>
                                            </div>






                                        </div>
                                    </div>
                                </div>


                                <!-- Product Images Repeater -->
                                <div class="form-group row mb-4">
                                    <label class="col-form-label text-md-right col-12 col-md-3 col-lg-3">Field Name</label>
                                    <div class="col-sm-12 col-md-8 image-repeater-wrapper">
                                        <?php $__currentLoopData = $post->table_content; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $content): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <div class="row control-group input-group mb-2">
                                            <div class="col-sm-12 col-md-12">
                                                <input type="text" name="field_name[]" class="form-control" style="margin-bottom:20px" value="<?php echo e($content->field_name); ?>" />
                                            </div>
                                            <div class="col-sm-12 col-md-12">
                                                <textarea class="summernote" name="value[]"><?php echo e($content->value); ?></textarea>
                                                <!-- <input type="text" name="value[]" class="form-control" value="<?php echo e($content->value); ?>" /> -->
                                            </div>
                                            <div class="col-sm-2 col-md-2">

                                                <button class="btn btn-danger remove-btn" type="button">Delete</button>
                                            </div>
                                        </div>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    </div>


                                    <!-- Hidden clone template -->
                                    <div class="clone" style="width: 100%;">
                                        <div class="row control-group input-group mb-2">
                                            <label class="col-form-label text-md-right col-12 col-md-3 col-lg-3"></label>
                                            <div class="col-sm-12 col-md-8">
                                                <div class="col-sm-12 col-md-12">
                                                    <input type="text" name="field_name[]" class="form-control" style="margin-bottom:20px" placeholder="Add Field Name Here" />
                                                </div>
                                                <div class="col-sm-12 col-md-12">
                                                    <textarea class="summernote" name="value[]"></textarea>
                                                    <!-- <input type="text" name="value[]" class="form-control" placeholder="Add Value Here" /> -->
                                                </div>
                                                <div class="col-sm-2 col-md-4">
                                                    <button class="btn btn-danger remove-btn" type="button">Delete</button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                        </div>

                       <?php if($post->draft == 'on'): ?>
                            <div class="form-group row mb-4">
                                    <label class="col-form-label text-md-right col-12 col-md-3 col-lg-3"> Is Draft</label>
                                    <div class="col-sm-12 col-md-7">
                                        <input type="checkbox" id="vehicle1" name="draft" value="on" checked>
                                    </div>
                                </div>
                        <?php endif; ?>

                        <?php if($post->draft != 'on'): ?>
                            <div class="form-group row mb-4">
                                    <label class="col-form-label text-md-right col-12 col-md-3 col-lg-3"> Is Draft</label>
                                    <div class="col-sm-12 col-md-7">
                                        <input type="checkbox" id="vehicle1" name="draft" value="on">
                                    </div>
                                </div>
                         <?php endif; ?>

                        <div class="form-group row mb-4">
                            <label class="col-form-label text-md-right col-12 col-md-3 col-lg-3"></label>
                            <div class="col-sm-12 col-md-7">
                                <button class="btn btn-success btn-increment" type="button">Add More</button>
                                <input type="submit" class="btn btn-primary px-4" value="Save Changes" />
                            </div>
                        </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>

</div>

</div>
</div>
</div>
</div>
</div>
</section>
</div>

<!-- Scripts -->
<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>

<script>
    function initSummernote(selector) {
        $(selector).summernote({
            height: 250,
            callbacks: {
                onImageUpload: function(files) {
                    sendFile(files[0], $(this));
                },
                onInit: function() {
                    let toolbar = $(this).next('.note-editor').find('.note-toolbar');
                    toolbar.append(`
                        <button type="button" class="btn btn-sm btn-light btn-lfm" data-type="Images">
                            <i class="note-icon-picture"></i> Browse
                        </button>
                    `);
                }
            }
        });
    }

    function sendFile(file, $editor) {
        var data = new FormData();
        data.append("upload", file);
        data.append("_token", $('meta[name="csrf-token"]').attr('content'));

        $.ajax({
            url: "/laravel-filemanager/upload?type=Images",
            method: "POST",
            data: data,
            contentType: false,
            processData: false,
            success: function(response) {
                if (response.url) {
                    $editor.summernote('insertImage', response.url);
                } else {
                    alert("Upload failed");
                }
            },
            error: function(jqXHR) {
                console.error("Upload error:", jqXHR);
                alert("Upload error: " + jqXHR.statusText);
            }
        });
    }

    function openLfm(callback, type = 'Images') {
        window.open('/laravel-filemanager?type=' + type, 'FileManager', 'width=900,height=600');
        window.SetUrl = callback;
    }





    $(document).ready(function() {
        const APP_URL = "<?php echo e(url('/')); ?>";

        function initSummernote(selector) {
            $(selector).summernote({
                height: 250,
                callbacks: {
                    onImageUpload: function(files) {
                        sendFile(files[0], $(this));
                    },
                    onInit: function() {
                        let toolbar = $(this).next('.note-editor').find('.note-toolbar');
                        toolbar.append(`
                        <button type="button" class="btn btn-sm btn-light btn-lfm" data-type="Images">
                            <i class="note-icon-picture"></i> Browse
                        </button>
                    `);
                    }
                }
            });
        }

        function sendFile(file, $editor) {
            var data = new FormData();
            data.append("upload", file);
            data.append("_token", $('meta[name="csrf-token"]').attr('content'));

            $.ajax({
                url: "/laravel-filemanager/upload?type=Images",
                method: "POST",
                data: data,
                contentType: false,
                processData: false,
                success: function(response) {
                    if (response.url) {
                        $editor.summernote('insertImage', response.url);
                    } else {
                        alert("Upload failed");
                    }
                },
                error: function(jqXHR) {
                    alert("Upload error: " + jqXHR.statusText);
                }
            });
        }

        function openLfm(callback, type = 'Images') {
            window.open('/laravel-filemanager?type=' + type, 'FileManager', 'width=900,height=600');
            window.SetUrl = callback;
        }

        function lfm(id, type, options) {
            let button = document.getElementById(id);

            button.addEventListener('click', function() {
                var route_prefix = (options && options.prefix) ? options.prefix : '/laravel-filemanager';
                var target_input = document.getElementById(button.getAttribute('data-input'));
                var target_preview = document.getElementById(button.getAttribute('data-preview'));

                window.open(route_prefix + '?type=' + (options.type || 'file'), 'FileManager', 'width=900,height=600');

                window.SetUrl = function(items) {
                    var file_path = items.map(item => item.url).join(',');
                    target_input.value = file_path;
                    target_input.dispatchEvent(new Event('change'));

                    target_preview.innerHTML = '';
                    items.forEach(item => {
                        let img = document.createElement('img');
                        img.setAttribute('style', 'height: 5rem');
                        img.setAttribute('src', item.thumb_url);
                        target_preview.appendChild(img);
                    });
                    target_preview.dispatchEvent(new Event('change'));
                };
            });
        }

        // ✅ Initialize Summernote
        $('.summernote').each(function() {
            initSummernote(this);
        });

        // ✅ Add new repeater block
        $('body').on('click', '.btn-increment', function() {
            let html = $('.clone').html();
            let newElement = $(html);
            $('.image-repeater-wrapper').append(newElement);
            newElement.find('.summernote').each(function() {
                // initSummernote(this);
            });
        });

        // ✅ Remove repeater block
        $('body').on('click', '.remove-btn', function() {
            $(this).closest('.control-group').remove();
        });

        // ✅ Handle image upload to Summernote via "Browse"
        $('body').on('click', '.btn-lfm', function() {
            let $note = $(this).closest('.note-editor').prev('.summernote');
            openLfm(function(urls) {
                if (typeof urls === 'string') {
                    $note.summernote('insertImage', urls);
                } else {
                    urls.forEach(function(url) {
                        $note.summernote('insertImage', url.url);
                    });
                }
            }, 'Images');
        });

        // ✅ Initialize LFM picker button
        lfm('lfm', 'image', {
            prefix: '/laravel-filemanager'
        });
    });
</script>


<!-- <script>
    document.addEventListener('DOMContentLoaded', function () {
        const selectElement = document.querySelector('select[name="type"]');

        selectElement.addEventListener('change', function () {
            const selectedValue = this.value;
            // const selectedText = this.options[this.selectedIndex].text;
            console.log(selectedValue);
            // console.log("Selected text:", selectedText);
        });
    });
</script> -->

<script>
    document.addEventListener('DOMContentLoaded', function() {
        const selectElement = document.querySelector('select[name="type"]');

        selectElement.addEventListener('change', function() {
            const selectedValue = this.value;
            console.log(selectedValue);

            // Hide all groups
            document.querySelectorAll('.field-group').forEach(group => {
                group.classList.add('d-none');
            });

            // Show the matching group
            const targetGroup = document.querySelector('.type-' + selectedValue);
            if (targetGroup) {
                targetGroup.classList.remove('d-none');

                // Init summernote if exists
                targetGroup.querySelectorAll('.summernote').forEach(el => {
                    if (!$(el).next('.note-editor').length) {
                        $(el).summernote(); // Or initSummernote(el) if you're using a custom function
                    }
                });
            }
        });
    });
</script>





<script>
document.addEventListener('DOMContentLoaded', function () {
  const selectElement = document.querySelector('select[name="type"]');

  selectElement.addEventListener('change', function () {
    const selectedValue = this.value;
    console.log(selectedValue);
    
    // Hide all groups
    document.querySelectorAll('.field-group').forEach(group => {
      group.classList.add('d-none');
    });

    // Show the matching group
    const targetGroup = document.querySelector('.type-' + selectedValue);
    if (targetGroup) {
      targetGroup.classList.remove('d-none');

      // Init summernote if exists
      targetGroup.querySelectorAll('.summernote').forEach(el => {
        if (!jQuery(el).next('.note-editor').length) {
          jQuery(el).summernote(); // Or initSummernote(el) if you're using a custom function
        }
      });
    }
  });
});
</script>




<script type="text/template" id="image-template">
    <div class="image-group mb-3" data-index="__INDEX__">
        <div class="input-group">
            <span class="input-group-btn">
                <a id="lfm___INDEX__" class="btn btn-primary lfm-btn" data-input="image___INDEX__" data-preview="holder___INDEX__">
                    <i class="fa fa-picture-o"></i> Choose
                </a>
            </span>
            <input id="image___INDEX__" class="form-control" type="text" name="image_gallery[]">
        </div>
        <div id="holder___INDEX__" style="margin-top:15px; max-height:200px;"></div>
        <button type="button" class="btn btn-danger btn-sm mt-2 btn-remove">Remove</button>
    </div>
</script>

<?php $__env->stopSection(); ?>
<?php echo $__env->make('backend.master', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\laragon\www\interprt\resources\views/backend/posts/edit_post.blade.php ENDPATH**/ ?>