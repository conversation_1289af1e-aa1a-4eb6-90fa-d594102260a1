<?php

namespace App\Http\Controllers;

use App\Models\EcocideBanner;
use Illuminate\Http\Request;

class EcocideBannerController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        //
         $Banners= EcocideBanner::latest()->get();
         return view('backend.ecocide_banner.index', compact('Banners'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
        return view('backend.ecocide_banner.create');
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        //
        $ecocide_banner = EcocideBanner::create([
            'title' => $request->title,
            'video' => $request->video,
            'details' => $request->details,
           
        ]);

        return redirect()->route('ecocide_banner')->with('success', 'Ecocide Banner Created Successfully');

    }

    /**
     * Display the specified resource.
     */
    public function show(EcocideBanner $ecocideBanner)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(EcocideBanner $ecocideBanner,$id)
    {
        //
        $ecocideBanner = EcocideBanner::findOrFail($id);
        return view('backend.ecocide_banner.edit', compact('ecocideBanner'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, EcocideBanner $ecocideBanner,$id)
    {
        //
        $ecocideBanner = EcocideBanner::findOrFail($id);
        $ecocideBanner->update([
            'title' => $request->title,
            'video' => $request->video,
            'details' => $request->details,
        ]);

        return redirect()->route('ecocide_banner')->with('success', 'Ecocide Banner Updated Successfully');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(EcocideBanner $ecocideBanner,$id)
    {
        //
        $ecocideBanner = EcocideBanner::findOrFail($id)->delete();


        return redirect()->route('ecocide_banner')->with('success', 'Ecocide Banner Deleted Successfully');
    }
   
}
