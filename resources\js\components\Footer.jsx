import React from 'react';
import { Link, useLocation } from 'react-router-dom'; // Add this import
const Footer = () => (
    <footer>
        <div className="container">
            <div className="row">
                <div className="col-12 col-md-6 mb-4 mb-md-0">
                    <div className="row d-flex justify-content-sm-between ">
                        <div className="col-sm-6 col-md-3">
                            <ul className="list-unstyled mb-3">
                                {[
                                    { label: 'About', href: '/about' },
                                    { label: 'Ecocide Study', href: '/ecocide' },
                                ].map((item, idx) => (
                                    <li key={idx}>
                                        <Link to={item.href} itemProp="url">{item.label}</Link>
                                    </li>
                                ))}

                            </ul>
                        </div>
                        <div className="col-sm-6 col-md-6">
                            <ul className="list-unstyled mb-3" style={{ maxWidth: 350, width: '100%', }}>
                                <li>
                                    <a  href="https://twitter.com/interprt" target="_blank" rel="noopener noreferrer">
                                        Twitter
                                    </a>
                                </li>
                                <li>
                                    <a  href="https://www.instagram.com/interprt_/?hl=en" target="_blank" rel="noopener noreferrer">
                                        Instagram
                                    </a>
                                </li>
                            </ul>
                        </div>
                    </div>
                    <div className="row g-4 d-none d-md-flex align-items-center justify-content-md-between ">
                        <div className="col-6 col-md-3">
                            <a href="/">
                                <img
                                    width="140"
                                    src="./logo_dark_hp.svg"
                                    alt="Interprt Logo"
                                    style={{ maxWidth: 120, height: 'auto' }}
                                />
                            </a>
                        </div>
                        <div className="col-md-8 col-6 col-md-6 ">
                            <ul className="bottom-list-unstyled" style={{ maxWidth: 350, width: '100%', marginLeft: 'auto' }}>
                                <li className="small">© INTERPRT 2025</li>
                            </ul>
                        </div>
                    </div>
                </div>
                <div className="col-12 col-md-6 pb-3 pb-md-0 d-flex justify-content-md-end">
                    <ul className="list-unstyled" style={{ maxWidth: 350, width: '100%',  paddingTop: window.innerWidth < 980 ? 0 : 0 }}>
                        <li>
                            INTERPRT AS <br />
                            Org.nr.: 932794012 <br />
                            Postal address: <br />
                            Vår Frue gate 8, 7013 Trondheim, Norway
                            <br /> <br />
                            <a href="mailto:<EMAIL>"><EMAIL></a>
                        </li>
                    </ul>
                </div>
            </div>
            {/* Responsive logo and copyright */}
            <div className="row g-4 d-md-none align-items-center">
                <div className="col-12 col-md-2 mb-2">
                    <a href="/">
                        <img
                            width="140"
                            src="./logo_dark_hp.svg"
                            alt="Interprt Logo"
                            style={{ maxWidth: 180, height: 'auto' }}
                        />
                    </a>
                </div>
                <div className="col-12 col-md-10">
                    <ul className="bottom-list-unstyled mb-0">
                        <li className="small">© INTERPRT 2025</li>
                    </ul>
                </div>
            </div>
        </div>
    </footer>
);

export default Footer;

