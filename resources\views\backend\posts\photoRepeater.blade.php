<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <title>Image Gallery Repeater</title>
    <link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css" />
    <!-- FontAwesome for icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/4.7.0/css/font-awesome.min.css" />
</head>
<body>
<div class="container mt-5">
   <form method="POST" action="{{ route('posts.store') }}" enctype="multipart/form-data">
    <!-- CSRF token for Lara<PERSON>, add if you want -->
    @csrf

    <div class="form-group row mb-4">
        <label class="col-form-label text-md-right col-12 col-md-3 col-lg-3">Image Gallery</label>
        <div class="col-sm-12 col-md-7">
            <div id="gallery-wrapper">
                <!-- Initial image field -->
                <div class="image-group mb-3" data-index="0">
                    <div class="input-group">
                        <span class="input-group-btn">
                            <a id="lfm_0" class="btn btn-primary lfm-btn" data-input="image_0" data-preview="holder_0">
                                <i class="fa fa-picture-o"></i> Choose
                            </a>
                        </span>
                        <input id="image_0" class="form-control" type="text" name="image_gallery[]">
                    </div>
                    <div id="holder_0" style="margin-top:15px; max-height:200px;"></div>
                    <button type="button" class="btn btn-danger btn-sm mt-2 btn-remove">Remove</button>
                </div>
            </div>

            <button type="button" class="btn btn-success btn-sm mt-2" id="add-photo">+ Add Photo</button>
        </div>
    </div>

    <div class="form-group row">
        <div class="col-sm-12 col-md-7 offset-md-3">
            <button type="submit" class="btn btn-primary">Submit</button>
        </div>
    </div>
</form>

    <!-- Hidden template to clone -->
    <div id="image-template" style="display:none;">
        <div class="image-group mb-3" data-index="__INDEX__">
            <div class="input-group">
                <span class="input-group-btn">
                    <a id="lfm___INDEX__" class="btn btn-primary lfm-btn" data-input="image___INDEX__" data-preview="holder___INDEX__">
                        <i class="fa fa-picture-o"></i> Choose
                    </a>
                </span>
                <input id="image___INDEX__" class="form-control" type="text" name="image_gallery[]">
            </div>
            <div id="holder___INDEX__" style="margin-top:15px; max-height:200px;"></div>
            <button type="button" class="btn btn-danger btn-sm mt-2 btn-remove">Remove</button>
        </div>
    </div>
</div>

<!-- jQuery and Bootstrap JS -->
<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
<script src="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/js/bootstrap.min.js"></script>

<!-- Include LFM Standalone button JS -->
<script src="/vendor/laravel-filemanager/js/stand-alone-button.js"></script>

<script>
    let imageIndex = 1;

    $(document).ready(function () {
        // Initialize LFM for the initial image field
        $('#lfm_0').filemanager('image', {prefix: '/laravel-filemanager'});

        // Add new photo input group
        $('#add-photo').click(function () {
            let template = $('#image-template').html();
            // Replace __INDEX__ with current index
            let newField = template.replace(/__INDEX__/g, imageIndex);

            // Append to gallery-wrapper
            $('#gallery-wrapper').append(newField);

            // Initialize LFM for the new button
            $('#lfm_' + imageIndex).filemanager('image', {prefix: '/laravel-filemanager'});

            imageIndex++;
        });

        // Remove image field group on click
        $('body').on('click', '.btn-remove', function () {
            $(this).closest('.image-group').remove();
        });
    });
</script>
</body>
</html>
