@extends('backend.master')
@section('main')

        <!-- Start app main Content -->
        <div class="main-content">
        <section class="section">
                
                <div class="section-body">
                  
              <link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css" />
    <!-- FontAwesome for icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/4.7.0/css/font-awesome.min.css" />     
                  
                    <div class="row">
                        <div class="col-12">
                            <div class="card">
                                <div class="card-header">
                                    
                                    <h4>Add Post</h4>
                                </div>
                                <div class="card-body">
                                    <form action="{{ route('posts.store')}}" method="POST">
                                        @csrf
                                    <div class="form-group row mb-4">
                                        <label class="col-form-label text-md-right col-12 col-md-3 col-lg-3">Title</label>
                                        <div class="col-sm-12 col-md-7">
                                             
                                            <input type="text" name="title" class="form-control">
                                        </div>
                                    </div>

                                    <div class="form-group row mb-4">
                                        <label class="col-form-label text-md-right col-12 col-md-3 col-lg-3">Publish Date</label>
                                        <div class="col-sm-12 col-md-7">
                                            <input type="date" name="publish_date" class="form-control">
                                        </div>
                                    </div>


                                      <div class="form-group row mb-4">
                                        <label class="col-form-label text-md-right col-12 col-md-3 col-lg-3">Category</label>
                                        <div class="col-sm-12 col-md-7">
                                            <select class="form-control selectric" name="type" required>
                                                <option style="display:none">Select Category</option>
                                                @foreach($category as $category)
                                                     <option value="{{$category->id}}">{{ $category->category_name }}</option>
                                                @endforeach
                                                
                                                </select>
                                            </div>
                                        </div>

                                          <div class="form-group row mb-4">
                                        <label class="col-form-label text-md-right col-12 col-md-3 col-lg-3">Featured Image</label>
                                        <div class="col-sm-12 col-md-7">
                                            <div class="input-group">
                                                <span class="input-group-btn">
                                                    <a id="lfm" data-input="thumbnail" data-preview="holder" class="btn btn-primary lfm-btn">
                                                        <i class="fa fa-picture-o"></i> Choose
                                                    </a>
                                                </span>
                                                <input id="thumbnail" class="form-control" type="text" name="image">
                                            </div>
                                            <div id="holder" style="margin-top:15px;max-height:200px;"></div>
                                          
                               
                                        </div>
                                    </div>

                                    <div class="form-group row mb-4">
                                        <label class="col-form-label text-md-right col-12 col-md-3 col-lg-3">Details</label>
                                        <div class="col-sm-12 col-md-7">

                                            <textarea class="summernote" name="details"></textarea>
                               
                                        </div>
                                        
                                    </div>
                                      <div class="form-group row mb-4">
                                        <label class="col-form-label text-md-right col-12 col-md-3 col-lg-3"></label>
                                              <div class="col-sm-12 col-md-7">
                                                     <div class="input-group">


                                                        <!-- category fields -->
                                                     <div id="dynamic-fields-container" style="width: 100%;">
                                                        <div class="field-group type-3 d-none">
                                                            <!-- Type 3 Fields -->
                                                            <div class="form-group mb-2">
                                                                <label class="form-label">Situation</label>
                                                                <input type="text" name="situation" class="form-control" placeholder="Situation">
                                                            </div>

                                                             <div class="form-group mb-2">
                                                                <label class="form-label">Period</label>
                                                                <input type="text" name="Period" class="form-control" placeholder="Period">
                                                            </div>



                                                            <div class="form-group mb-2">
                                                                <label class="form-label">Venue</label>
                                                                <input type="text" name="venue" class="form-control" placeholder="Venue">
                                                            </div>

                                                            <div class="form-group mb-2">
                                                                <label class="form-label">Methodology</label>
                                                                <input type="text" name="methodology" class="form-control" placeholder="Methodology">
                                                            </div>

                                                            <div class="form-group mb-2">
                                                                <label class="form-label">Partners</label>
                                                                <input type="text" name="partners" class="form-control" placeholder="Partners">
                                                            </div>

                                                            <!-- Repeat for all fields -->
                                                            <div class="form-group mb-2">
                                                             <label class="form-label">Project Team</label>
                                                                <textarea name="project_team" class="form-control summernote"></textarea>
                                                            </div>
                                                        </div>

                                                        <div class="field-group type-4 d-none">
                                                             <div class="form-group mb-2">
                                                                <label class="form-label">Location </label>
                                                                <input type="text" name="venue_one" class="form-control" placeholder="Situation">
                                                            </div>

                                                            <div class="form-group mb-2">
                                                                <label class="form-label">Date </label>
                                                                <input type="text" name="date" class="form-control" placeholder="Situation">
                                                            </div>

                                                             <div class="form-group mb-2">
                                                                <label class="form-label">PARTNERS </label>
                                                                <input type="text" name="partner_one" class="form-control" placeholder="Situation">
                                                            </div>

                                                              <div class="form-group mb-2">
                                                                <label class="form-label">Type </label>
                                                                <input type="text" name="type_one" class="form-control" placeholder="Situation">
                                                            </div>
                                                            <!-- Add other fields -->
                                                        </div>

                                                        <div class="field-group type-5 d-none">
                                                            <!-- Type 5 Fields, similar to type 4 -->
                                                            <div class="form-group mb-2">
                                                                <label class="form-label">Location </label>
                                                                <input type="text" name="venue_two" class="form-control" placeholder="Situation">
                                                            </div>

                                                            <div class="form-group mb-2">
                                                                <label class="form-label">Date </label>
                                                                <input type="text" name="date_one" class="form-control" placeholder="Situation">
                                                            </div>

                                                             <div class="form-group mb-2">
                                                                <label class="form-label">PARTNERS </label>
                                                                <input type="text" name="partner_two" class="form-control" placeholder="Situation">
                                                            </div>

                                                              <div class="form-group mb-2">
                                                                <label class="form-label">Type </label>
                                                                <input type="text" name="type_two" class="form-control" placeholder="Situation">
                                                            </div>
                                                            <!-- Add other fields -->
                                                        </div>

                                                            <div class="field-group type-6 d-none">
                                                                <!-- Type 6 Fields -->
                                                                <div class="form-group mb-2">
                                                                    <label class="form-label">Book Title </label>
                                                                    <input type="text" name="book_title" class="form-control" placeholder="Situation">
                                                                </div>

                                                                 <div class="form-group mb-2">
                                                                    <label class="form-label">Date </label>
                                                                    <input type="text" name="date_three" class="form-control" placeholder="Situation">
                                                                </div>

                                                                <div class="form-group mb-2">
                                                                    <label class="form-label">URL </label>
                                                                    <input type="text" name="url" class="form-control" placeholder="Situation">
                                                                </div>
                                                                <!-- Add other fields -->
                                                            </div>
                                                        </div>






                                                     </div>
                                              </div>
                                      </div>

                                    

    <div class="form-group row mb-4">
            <label class="col-form-label text-md-right col-12 col-md-3 col-lg-3">Image Gallery</label>
            <div class="col-sm-12 col-md-7">
                <div id="gallery-wrapper">
                    <!-- Initial image field -->
                    <div class="image-group mb-3" data-index="0">
                        <div class="input-group">
                            <span class="input-group-btn">
                                <a id="lfm_0" class="btn btn-primary lfm-btn" data-input="image_0" data-preview="holder_0">
                                    <i class="fa fa-picture-o"></i> Choose
                                </a>
                            </span>
                            <input id="image_0" class="form-control" type="text" name="image_gallery[]">
                        </div>
                        <div id="holder_0" style="margin-top:15px; max-height:200px;"></div>
                        <button type="button" class="btn btn-danger btn-sm mt-2 btn-remove">Remove</button>
                    </div>
                </div>

                <button type="button" class="btn btn-success btn-sm mt-2" id="add-photo">+ Add Photo</button>
            </div>
        </div>









        




                                      <!-- Product Images Repeater -->
                                <div class="form-group row mb-4">
                                    <label class="col-form-label text-md-right col-12 col-md-3 col-lg-3">Field Name</label>
                                    <div class="col-sm-12 col-md-7">
                                        <!-- Wrapper for all image inputs -->
                                        <div class="image-repeater-wrapper">
                                            <div class="row control-group input-group mb-2">
                                                
                                                
                                                    
                                                <div class="col-sm-2  col-md-2">
                                                    
                                                </div>
                                            </div>
                                        </div>

                                        <!-- Hidden clone template -->
                                        <div class="clone">
                                            <div class="row control-group input-group mb-2">
                                            <div class="col-sm-12 col-md-12">
                                                     <input type="text" name="field_name[]" class="form-control" style="margin-bottom:20px" placeholder="Add Field Name Here" />
                                                </div>
                                                <div class="col-sm-12 col-md-12">
                                                    <textarea class="summernote" name="value[]"></textarea>
                                                     <!-- <input type="text" name="value[]" class="form-control" placeholder="Add Value Here" /> -->
                                                </div>
                                                <div class="col-sm-2 col-md-4">
                                                    <button class="btn btn-danger remove-btn" type="button">Delete</button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                    
                                    <div class="form-group row mb-4">
                                        <label class="col-form-label text-md-right col-12 col-md-3 col-lg-3"> Is Draft</label>
                                        <div class="col-sm-12 col-md-7">
                                            <input type="checkbox" id="vehicle1" name="draft" value="on">
                                        </div>
                                    </div>

                                   
                                 
                                    <div class="form-group row mb-4">
                                        <label class="col-form-label text-md-right col-12 col-md-3 col-lg-3"></label>
                                        <div class="col-sm-12 col-md-7">
                                            <button class="btn btn-success btn-increment" type="button">Add More</button>
                                        <input type="submit" class="btn btn-primary px-4" value="Save Changes" />
                                        </div>
                                    </div>
                                </form>
                                </div>
                            </div>
                        </div>
                    </div>
                   
                                    </div>
                                   
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </section>
        </div>

<script>
    function initSummernote(selector) {
        jQuery(selector).summernote({
            height: 250,
            callbacks: {
                onImageUpload: function(files) {
                    sendFile(files[0], jQuery(this));
                },
                onInit: function () {
                    let toolbar = jQuery(this).next('.note-editor').find('.note-toolbar');
                    toolbar.append(`
                        <button type="button" class="btn btn-sm btn-light btn-lfm" data-type="Images">
                            <i class="note-icon-picture"></i> Browse
                        </button>
                    `);
                }
            }
        });
    }

    function sendFile(file, $editor) {
        var data = new FormData();
        data.append("upload", file);
        data.append("_token", jQuery('meta[name="csrf-token"]').attr('content'));

        $.ajax({
            url: "/laravel-filemanager/upload?type=Images",
            method: "POST",
            data: data,
            contentType: false,
            processData: false,
            success: function (response) {
                if (response.url) {
                    $editor.summernote('insertImage', response.url);
                } else {
                    alert("Upload failed");
                }
            },
            error: function (jqXHR) {
                console.error("Upload error:", jqXHR);
                alert("Upload error: " + jqXHR.statusText);
            }
        });
    }

    function openLfm(callback, type = 'Images') {
        window.open('/laravel-filemanager?type=' + type, 'FileManager', 'width=900,height=600');
        window.SetUrl = callback;
    }


    


    jQuery(document).ready(function () {
    const APP_URL = "{{ url('/') }}";

    function initSummernote(selector) {
        jQuery(selector).summernote({
            height: 250,
            callbacks: {
                onImageUpload: function(files) {
                    sendFile(files[0], jQuery(this));
                },
                onInit: function () {
                    let toolbar = jQuery(this).next('.note-editor').find('.note-toolbar');
                    toolbar.append(`
                        <button type="button" class="btn btn-sm btn-light btn-lfm" data-type="Images">
                            <i class="note-icon-picture"></i> Browse
                        </button>
                    `);
                }
            }
        });
    }

    function sendFile(file, $editor) {
        var data = new FormData();
        data.append("upload", file);
        data.append("_token", jQuery('meta[name="csrf-token"]').attr('content'));

        $.ajax({
            url: "/laravel-filemanager/upload?type=Images",
            method: "POST",
            data: data,
            contentType: false,
            processData: false,
            success: function (response) {
                if (response.url) {
                    $editor.summernote('insertImage', response.url);
                } else {
                    alert("Upload failed");
                }
            },
            error: function (jqXHR) {
                alert("Upload error: " + jqXHR.statusText);
            }
        });
    }

    function openLfm(callback, type = 'Images') {
        window.open('/laravel-filemanager?type=' + type, 'FileManager', 'width=900,height=600');
        window.SetUrl = callback;
    }

    function lfm(id, type, options) {
        let button = document.getElementById(id);

        button.addEventListener('click', function () {
            var route_prefix = (options && options.prefix) ? options.prefix : '/laravel-filemanager';
            var target_input = document.getElementById(button.getAttribute('data-input'));
            var target_preview = document.getElementById(button.getAttribute('data-preview'));

            window.open(route_prefix + '?type=' + (options.type || 'file'), 'FileManager', 'width=900,height=600');

            window.SetUrl = function (items) {
                var file_path = items.map(item => item.url).join(',');
                target_input.value = file_path;
                target_input.dispatchEvent(new Event('change'));

                target_preview.innerHTML = '';
                items.forEach(item => {
                    let img = document.createElement('img');
                    img.setAttribute('style', 'height: 5rem');
                    img.setAttribute('src', item.thumb_url);
                    target_preview.appendChild(img);
                });
                target_preview.dispatchEvent(new Event('change'));
            };
        });
    }

    // ✅ Initialize Summernote
    jQuery('.summernote').each(function () {
        initSummernote(this);
    });

    // ✅ Add new repeater block
    jQuery('body').on('click', '.btn-increment', function () {
        let html = jQuery('.clone').html();
        let newElement = jQuery(html);
        jQuery('.image-repeater-wrapper').append(newElement);
        newElement.find('.summernote').each(function () {
            // initSummernote(this);
        });
    });

    // ✅ Remove repeater block
    jQuery('body').on('click', '.remove-btn', function () {
        jQuery(this).closest('.control-group').remove();
    });

    // ✅ Handle image upload to Summernote via "Browse"
    jQuery('body').on('click', '.btn-lfm', function () {
        let $note = jQuery(this).closest('.note-editor').prev('.summernote');
        openLfm(function (urls) {
            if (typeof urls === 'string') {
                $note.summernote('insertImage', urls);
            } else {
                urls.forEach(function (url) {
                    $note.summernote('insertImage', url.url);
                });
            }
        }, 'Images');
    });

    // ✅ Initialize LFM picker button
    lfm('lfm', 'image', { prefix: '/laravel-filemanager' });
});

</script>


<!-- <script>
    document.addEventListener('DOMContentLoaded', function () {
        const selectElement = document.querySelector('select[name="type"]');

        selectElement.addEventListener('change', function () {
            const selectedValue = this.value;
            // const selectedText = this.options[this.selectedIndex].text;
            console.log(selectedValue);
            // console.log("Selected text:", selectedText);
        });
    });
</script> -->

<script>
document.addEventListener('DOMContentLoaded', function () {
  const selectElement = document.querySelector('select[name="type"]');

  selectElement.addEventListener('change', function () {
    const selectedValue = this.value;
    console.log(selectedValue);
    
    // Hide all groups
    document.querySelectorAll('.field-group').forEach(group => {
      group.classList.add('d-none');
    });

    // Show the matching group
    const targetGroup = document.querySelector('.type-' + selectedValue);
    if (targetGroup) {
      targetGroup.classList.remove('d-none');

      // Init summernote if exists
      targetGroup.querySelectorAll('.summernote').forEach(el => {
        if (!jQuery(el).next('.note-editor').length) {
          jQuery(el).summernote(); // Or initSummernote(el) if you're using a custom function
        }
      });
    }
  });
});
</script>



<!-- jQuery and Bootstrap JS -->
<script src="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/js/bootstrap.min.js"></script>

<!-- Include LFM Standalone button JS -->
<script src="/vendor/laravel-filemanager/js/stand-alone-button.js"></script>

<script>
    let imageIndex = 1;

   jQuery(document).ready(function () {
    // Initialize LFM for the initial image field
    jQuery('#lfm_0').filemanager('image', {prefix: '/laravel-filemanager'});

    // Add new photo input group
    jQuery('#add-photo').click(function () {
        let template = jQuery('#image-template').html();
        let newField = template.replace(/__INDEX__/g, imageIndex);
        jQuery('#gallery-wrapper').append(newField);
        jQuery('#lfm_' + imageIndex).filemanager('image', {prefix: '/laravel-filemanager'});
        imageIndex++;
    });

    // Remove image field group on click
    jQuery('body').on('click', '.btn-remove', function () {
        jQuery(this).closest('.image-group').remove();
    });
});
</script>

<script type="text/template" id="image-template">
    <div class="image-group mb-3" data-index="__INDEX__">
        <div class="input-group">
            <span class="input-group-btn">
                <a id="lfm___INDEX__" class="btn btn-primary lfm-btn" data-input="image___INDEX__" data-preview="holder___INDEX__">
                    <i class="fa fa-picture-o"></i> Choose
                </a>
            </span>
            <input id="image___INDEX__" class="form-control" type="text" name="image_gallery[]">
        </div>
        <div id="holder___INDEX__" style="margin-top:15px; max-height:200px;"></div>
        <button type="button" class="btn btn-danger btn-sm mt-2 btn-remove">Remove</button>
    </div>
</script>


@endsection
