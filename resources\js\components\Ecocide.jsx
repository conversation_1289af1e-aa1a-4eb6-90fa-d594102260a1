import axios from 'axios';
import React, { useEffect, useRef, useState } from 'react';

const Ecocide = () => {
    const [ecocideStudies, setEcocideStudies] = useState([]);
    const [ecocideBanner, setEcocideBanner] = useState(null);
    const [loading, setLoading] = useState(true);

    useEffect(() => {
        axios.get('/api/ecocide_studies')
            .then(response => {
                setEcocideStudies(response.data);
                setLoading(false);
            })
            .catch(error => {
                console.error('Error fetching ecocide studies:', error);
                setLoading(false);
            });

        axios.get('/api/EcocideBanner')
            .then(response => {
                console.log('Banner API response:', response.data); // Add this
                setEcocideBanner(response.data);
            })
            .catch(error => {
                console.error('Error fetching ecocide banner:', error);
            });
    }, []);

    return (
        <div className="about-page-wrapper">
            {/* Ecocide Banner Section */}
            {ecocideBanner && ecocideBanner.length > 0 && ecocideBanner.map((banner, idx) => (
                <div className="containerss" key={idx}>
                    {/* Render video if present */}
                    {banner.video && (
                        <>
                            <div className="ecocide-banner-video" style={{ marginTop: '20px' }}>
                                <iframe
                                    src={banner.video}
                                    allow="autoplay; fullscreen; picture-in-picture"
                                    allowFullScreen
                                    title={`ecocide-video-${idx}`}
                                ></iframe>
                                {/* If you're using iframe for video, the <video> tag may be redundant */}
                                {/* <video src={banner.video} controls /> */}
                            
                            </div>
                        </>
                    )}
                </div>
            ))}


            <div className="container">
                <div className="row">
                    <div className="col-lg-12">
                        <div className="about-content">
                            {loading ? (
                                <div className="loading-spinner"></div>
                            ) : (
                                <div className='ecocide-studies'>
                                    {ecocideStudies.map((item, idx) => (
                                        <div key={idx} className="single-ecocide-study">
                                            <h3>{item.title}</h3>
                                            <div dangerouslySetInnerHTML={{ __html: item.details }}></div>
                                        </div>
                                    ))}
                                </div>
                            )}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );
};

export default Ecocide;