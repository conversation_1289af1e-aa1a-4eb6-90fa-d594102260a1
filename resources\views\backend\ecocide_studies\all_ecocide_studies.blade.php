@extends('backend.master')
@section('main')
        <!-- Start app main Content -->
        <div class="main-content">
        <section class="section">
                

                <div class="section-body">
                    
                    <div class="row">
                        <div class="col-12 col-md-12 col-lg-12">
                            <div class="card">
                                <div class="card-header">
                                    <h4>Ecocide Studies</h4>
                                </div>
                                <div class="card-body">
                                    <div class="table-responsive">
                                        <table class="table table-bordered table-md v_center">
                                            <tr>
                                                <th>#</th>
                                                <th>Image</th>
                                                <th>Title</th>
                                            <th>Publish Date</th>
                                                <th>Details</th>
                                               
                                                <th>Action</th>
                                            </tr>
                                            @foreach($ecocide_studies as $key=> $ecocide_studies)
                                            <tr>
                                                <td>{{$loop->iteration}}</td>
                                                <td>
                                                    @if($ecocide_studies->image)
                                                        <img src="{{ $ecocide_studies->image }}" alt="{{ $ecocide_studies->title }}" style="max-width: 100px; max-height: 80px;">
                                                    @else
                                                        <span class="text-muted">No Image</span>
                                                    @endif
                                                </td>
                                                <td>{{ $ecocide_studies->title }}</td>
                                                <td>{{ $ecocide_studies->publish_date }}</td>
                                                <td>{!! $ecocide_studies->details !!}</td>
                                            
                                                <td><a href="{{ route('ecocide_studies.edit', $ecocide_studies->id) }}" class="btn btn-secondary">Edit</a>
                                                <a href="{{ route('ecocide_studies.delete', $ecocide_studies->id) }}" class="btn btn-danger" onclick="return confirm('Are you sure you want to delete this post?');">Delete</a>
                                            </td>
                                            </tr>
                                            @endforeach
                                          
                                        </table> 
                                        <!-- Pagination links -->
                                             
                                    </div>
                                </div>
                               
                            </div>
                        </div>
                        
                    </div>
                 
                    
                </div>
            </section>
        </div>
@endsection