<?php

use App\Http\Controllers\ProfileController;
use App\Http\Controllers\PostsController;
use App\Http\Controllers\CategoryController;
use App\Http\Controllers\DashboardController;
use App\Http\Controllers\AboutController;
use App\Http\Controllers\TeamCategoryController;
use App\Http\Controllers\SiteSettingController;
use App\Http\Controllers\EcocideStudiesController;
use App\Http\Controllers\EcocideBannerController;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\TeamController;
use App\Models\about;
use App\Models\ecocide_studies;
use App\Models\posts;
use App\Models\team;
use App\Models\EcocideBanner;

Route::get('/', function () {
    return view('frontend.index');
});

// frontend related routes

Route::get('/api/about', function () {
    return about::first();
});

Route::get('/api/teams', function () {
    return team::with('team_category')->where('category_name', '!=', 'Past Members')->orderBy('id', "DESC")->get();
});

Route::get('/api/past_teams', function () {
    return team::with('team_category')->where('category_name', 'Past Members')->orderBy('id', "ASC")->get();
});

Route::get('/api/ecocide_studies', function () {
    return ecocide_studies::orderBy('id', "DESC")->get();
});
Route::get('/api/EcocideBanner', function () {
    return EcocideBanner::orderBy('id', "DESC")->get();
});

Route::get('/api/insights', function () {
    $filter = request()->query('filter', 'all');
    $limit = (int)request()->query('limit', 10);
    $limitP = (int)request()->query('limit', 1000);
    $offset = (int)request()->query('offset', 0);
    $page = (int)request()->query('page', 0);
    
    if( $page == 2 ){
        $offset = 25;
    } else if( $page > 2 ) {
        $offset = ($page - 1) * $limit + 25; 
    }

    // Base query
    $query = App\Models\posts::with('category', 'table_content', 'image_gallery')
        ->orderBy('publish_date', 'DESC')->whereNull('draft')
        ->orderBy('id', 'DESC');
    
    // Apply filter if not 'all' and not 'publications'
    if ($filter !== 'all' && $filter !== 'publications') {
        $query->whereHas('category', function ($q) use ($filter) {
            $q->where('category_name', $filter);
        });
    }
    

    // Special case for publications
    // if ($filter == 'publications') {
    //     $query->where('type', 6);
    //     return $query->skip($offset)->get(); // removed ->take($limit)
    // } else {
    //     $query->where('type', '!=', 6);
    // }
    // Special case for publications
    if ($filter == 'publications') {
        $query->where('type', 6);
        return $query->skip($offset)->take($limitP)->get();
    }else{
        $query->where('type', '!=', 6);
    }
    
    // Get total count for pagination
    $total = $query->count();
    
    // Get items for current page
    $items = $query->skip($offset)->take($limit)->get();
    
    return [
        'items' => $items,
        'total' => $total,
        'offset' => $offset,
        'limit' => $limit
    ];
});

    Route::get('/admin', [DashboardController::class, 'index'])->middleware(['auth', 'verified'])->name('dashboard');
   

    Route::middleware('auth')->group(function () {
    Route::get('/profile', [ProfileController::class, 'edit'])->name('profile.edit');
    Route::patch('/profile', [ProfileController::class, 'update'])->name('profile.update');
    Route::delete('/profile', [ProfileController::class, 'destroy'])->name('profile.destroy');


    // Posts All Route
    Route::controller(PostsController::class)->group(function () {
        Route::get('/post', 'index')->name('posts');
        Route::get('/add/post', 'create')->name('posts.add');
        Route::post('/store/post', 'store')->name('posts.store');
        
        Route::get('/post/edit/{id}', 'edit')->name('posts.edit');
        Route::get('/test/edit/{id}', 'test')->name('test.post');
        Route::put('/update/post/{id}', 'update')->name('posts.update');
        Route::get('/post/{id}','filterCategory')->name('filter.category');
        Route::get('/delete/post/{id}', 'destroy')->name('posts.delete');
    });


       // ecocide_studies All Route
    Route::controller(EcocideStudiesController::class)->group(function () {
        Route::get('/ecocide_studies', 'index')->name('ecocide_studies');
        Route::get('/add/ecocide_studies', 'create')->name('ecocide_studies.add');
        Route::post('/store/ecocide_studies', 'store')->name('ecocide_studies.store');
        Route::get('/ecocide_studies/edit/{id}', 'edit')->name('ecocide_studies.edit');
        Route::put('/update/ecocide_studies/{id}', 'update')->name('ecocide_studies.update');
        Route::get('/delete/ecocide_studies/{id}', 'destroy')->name('ecocide_studies.delete');
    });



        // ecocide_Banners All Route
    Route::controller(EcocideBannerController::class)->group(function () {
        Route::get('/ecocide_banner', 'index')->name('ecocide_banner');
        Route::get('/add/ecocide_banner', 'create')->name('ecocide_banner.add');
        Route::post('/store/ecocide_banner', 'store')->name('ecocide_banner.store');
        Route::get('/ecocide_banner/edit/{id}', 'edit')->name('ecocide_banner.edit');
        Route::put('/update/ecocide_banner/{id}', 'update')->name('ecocide_banner.update');
        Route::get('/delete/ecocide_banner/{id}', 'destroy')->name('ecocide_banner.delete');
    });




    Route::group(['prefix' => 'laravel-filemanager'], function () {
        \UniSharp\LaravelFilemanager\Lfm::routes();
    });

    // Category All Route
    Route::controller(CategoryController::class)->group(function () {
        Route::get('/category', 'index')->name('category');
        Route::get('/add/category', 'create')->name('category.add');
        Route::post('/store/category', 'store')->name('category.store');
        Route::get('/category/edit/{id}', 'edit')->name('category.Edit');
        Route::put('/update/category/{id}', 'update')->name('category.update');
        Route::get('/delete/category/{id}', 'destroy')->name('category.delete');
    });


    // About All Route
    Route::controller(AboutController::class)->group(function () {
        Route::get('/admin/about', 'index')->name('about');
         Route::get('/photo/repeater', 'photo')->name('photoRepeater');
        // Route::get('/add/category', 'create')->name('category.add');
        // Route::post('/store/category', 'store')->name('category.store');
        Route::get('/admin/about/edit/{id}', 'edit')->name('about.Edit');
        Route::post('/admin/update', 'update')->name('about.update');
        // Route::get('/delete/category/{id}', 'destroy')->name('category.delete');

    });

    // team All Route
    Route::controller(TeamController::class)->group(function () {
        Route::get('/team', 'index')->name('team');
        Route::get('/add/team', 'create')->name('team.add');
        Route::post('/store/team', 'store')->name('team.store');
        Route::get('/team/edit/{id}', 'edit')->name('team.edit');
        Route::post('/update/team', 'update')->name('team.update');
        Route::get('/delete/team/{id}', 'destroy')->name('team.delete');
    });

    // teamcategory All Route
    Route::controller(TeamCategoryController::class)->group(function () {
        Route::get('/team_category', 'index')->name('team_category');
        Route::get('/add/team_category', 'create')->name('team_category.add');
        Route::post('/store/team_category', 'store')->name('team_category.store');
        Route::get('/admin/team_category/edit/{id}', 'edit')->name('team_category.Edit');
        Route::post('/admin/team_category/update', 'update')->name('team_category.update');
        Route::get('/delete/team_category/{id}', 'destroy')->name('team_category.delete');
    });



    Route::group(['prefix' => 'laravel-filemanager', 'middleware' => ['web', 'auth']], function () {
        \UniSharp\LaravelFilemanager\Lfm::routes();
    });


    // siteSetting Route
    Route::controller(SiteSettingController::class)->group(function () {
        Route::get('/settings', 'index')->name('settings');
        Route::get('/add/setting', 'create')->name('setting.add');
        Route::post('/store/setting', 'store')->name('setting.store');
        Route::get('/setting/edit/{id}', 'edit')->name('setting.edit');
        Route::post('/update/setting', 'update')->name('setting.update');
        Route::get('/delete/setting/{id}', 'destroy')->name('setting.delete');
    });
});

require __DIR__.'/auth.php';

// Handle React Router paths - this should be at the end of your routes file
Route::get('/{path?}', function () {
    return view('frontend.index');
})->where('path', '(?!api|admin|login|register|storage).*');


