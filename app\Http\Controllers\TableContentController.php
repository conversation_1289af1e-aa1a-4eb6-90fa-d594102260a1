<?php

namespace App\Http\Controllers;

use App\Models\table_content;
use Illuminate\Http\Request;

class TableContentController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        //
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        //
    }

    /**
     * Display the specified resource.
     */
    public function show(table_content $table_content)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(table_content $table_content)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, table_content $table_content)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(table_content $table_content)
    {
        //
    }
}
