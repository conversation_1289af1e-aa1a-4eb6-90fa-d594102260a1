<?php

namespace App\Models;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use App\Models\category;
use App\Models\table_content;
use App\Models\image_gallery;
class posts extends Model
{
    // 
     use HasFactory;
    protected $guarded = []; 
    protected $table = 'posts'; // make sure this matches your DB table
    public function table_content()
    {
        return $this->hasMany(table_content::class, 'post_id');
    }

    public function image_gallery()
    {
        return $this->hasMany(image_gallery::class, 'post_id');
    }


   public function category()
    {
        return $this->belongsTo(category::class, 'type');
    }
}
