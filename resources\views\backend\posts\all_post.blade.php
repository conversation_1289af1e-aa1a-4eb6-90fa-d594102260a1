@extends('backend.master')
@section('main')
<!-- Start app main Content -->
<div class="main-content">
    <section class="section">
        <div class="section-body">
            <div class="row">
                <div class="col-12 col-md-12 col-lg-12">
                    <div class="card">
                        <div class="card-header">
                            <div class="card-header-left">
                                <h4>Post</h4>
                                <div class="">
                                    <button id="publishTabBtn" class="btn btn-primary mr-2" type="button">Publish</button>
                                    <button id="draftTabBtn" class="btn btn-outline-primary" type="button">Draft</button>
                                </div>
                            </div>
                            <div class="card-header-right">
                                <form method="GET" action="{{ route('posts') }}" id="searchForm">
                                    <div class="input-group">
                                        <input type="text" class="form-control" name="search"
                                            placeholder="Search posts by title, content, or category..."
                                            value="{{ $search ?? '' }}">
                                        <div class="input-group-append">
                                            <button class="btn btn-primary" type="submit">
                                                <i class="fas fa-search"></i> Search
                                            </button>
                                            @if($search)
                                                <a href="{{ route('posts') }}" class="btn btn-secondary">
                                                    <i class="fas fa-times"></i> Clear
                                                </a>
                                            @endif
                                        </div>
                                    </div>
                                </form>
                            </div>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table id="publishTable" class="table table-bordered table-md v_center">
                                    <tr>
                                        <th>#</th>
                                        <th>Title</th>
                                        <th>Type</th>
                                        <th>Action</th>
                                    </tr>
                                    @foreach($posts as $key=> $post)
                                    <tr>
                                        <td>{{$loop->iteration}}</td>
                                        <td>{{ $post->title }}</td>
                                        <td>{{ $post->category->category_name ?? 'No Category' }}</td>
                                        <td>
                                            <a href="{{ route('posts.edit', $post->id) }}" class="btn btn-secondary">Edit</a>
                                            <a href="{{ route('posts.delete', $post->id) }}" class="btn btn-danger" onclick="return confirm('Are you sure you want to delete this post?');">Delete</a>
                                        </td>
                                    </tr>
                                    @endforeach
                                </table>

                                <table id="draftTable" class="table table-bordered table-md v_center" style="display:none;">
                                    <tr>
                                        <th>#</th>
                                        <th>Image</th>
                                        <th>Title</th>
                                        <th>Type</th>
                                        <th>Details</th>
                                        <th>Other Information</th>
                                        <th>Action</th>
                                    </tr>
                                    @foreach($drafts as $key=> $post)
                                    <tr>
                                        <td>{{$loop->iteration}}</td>
                                        <td>
                                            @if($post->image)
                                            <img src="{{ $post->image }}" alt="{{ $post->title }}" style="max-width: 100px; max-height: 80px;">
                                            @else
                                            <span class="text-muted">No Image</span>
                                            @endif
                                        </td>
                                        <td>{{ $post->title }}</td>
                                        <td>{{ $post->category->category_name ?? 'No Category' }}</td>
                                        <td>{!! $post->details !!}</td>
                                        <td>
                                            @foreach($post->table_content as $c_table)
                                                <span style="padding-right:50px"><b>{{ $c_table->field_name }}</b></span>
                                                {!! $c_table->value !!}<br>
                                            @endforeach
                                        </td>
                                        <td>
                                            <a href="{{ route('posts.edit', $post->id) }}" class="btn btn-secondary">Edit</a>
                                            <a href="{{ route('posts.delete', $post->id) }}" class="btn btn-danger" onclick="return confirm('Are you sure you want to delete this post?');">Delete</a>
                                        </td>
                                    </tr>
                                    @endforeach
                                </table>

                                <!-- Published Posts Results Info and Pagination -->
                                <div id="publishPagination">
                                    @if($search)
                                        <div class="mb-2">
                                            <small class="text-muted">
                                                Found {{ $posts->total() }} published post(s) matching your search.
                                            </small>
                                        </div>
                                    @endif
                                    {{ $posts->links() }}
                                </div>

                                <!-- Draft Posts Results Info and Pagination -->
                                <div id="draftPagination" style="display:none;">
                                    @if($search)
                                        <div class="mb-2">
                                            <small class="text-muted">
                                                Found {{ $drafts->total() }} draft post(s) matching your search.
                                            </small>
                                        </div>
                                    @endif
                                    {{ $drafts->links() }}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const publishBtn = document.getElementById('publishTabBtn');
    const draftBtn = document.getElementById('draftTabBtn');
    const publishTable = document.getElementById('publishTable');
    const draftTable = document.getElementById('draftTable');
    const publishPagination = document.getElementById('publishPagination');
    const draftPagination = document.getElementById('draftPagination');

    publishBtn.addEventListener('click', function() {
        publishTable.style.display = '';
        draftTable.style.display = 'none';
        publishPagination.style.display = '';
        draftPagination.style.display = 'none';
        publishBtn.classList.add('btn-primary');
        publishBtn.classList.remove('btn-outline-primary');
        draftBtn.classList.remove('btn-primary');
        draftBtn.classList.add('btn-outline-primary');
    });

    draftBtn.addEventListener('click', function() {
        publishTable.style.display = 'none';
        draftTable.style.display = '';
        publishPagination.style.display = 'none';
        draftPagination.style.display = '';
        draftBtn.classList.add('btn-primary');
        draftBtn.classList.remove('btn-outline-primary');
        publishBtn.classList.remove('btn-primary');
        publishBtn.classList.add('btn-outline-primary');
    });
});
</script>
@endsection