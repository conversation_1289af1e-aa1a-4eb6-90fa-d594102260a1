@extends('backend.master')
@section('main')
        <!-- Start app main Content -->
        <div class="main-content">
            <section class="section">
                <div class="row">
                    <div class="col-lg-4 col-md-4 col-sm-12">
                       
                    <a href=" {{ route('posts') }} ">
                        <div class="card card-statistic-2">
                        
                            <div class="card-icon shadow-primary bg-primary">
                            <i class="fas fa-archive"></i>
                            </div>
                            <div class="card-wrap">
                            <div class="card-header">
                                <h4>Total Post</h4>
                            </div>
                            <div class="card-body">
                              {{ $total_posts }}
                            </div>
                            </div>
                        </div>
                    </a>

                        </div>
                    
                    <div class="col-lg-4 col-md-4 col-sm-12">
                    <a href="{{ route('category') }}">
                        <div class="card card-statistic-2">
                        
                            <div class="card-icon shadow-primary bg-primary">
                                <i class="fas fa-archive"></i>
                            </div>
                            <div class="card-wrap">
                                <div class="card-header">
                                    <h4>Total Category</h4>
                                </div>
                            <div class="card-body">
                               {{ $total_category }}
                            </div>
                            </div>
                        </div>
                    </a>
                    </div>


                @foreach($categories as $category)

                    <div class="col-lg-4 col-md-4 col-sm-12">
                    <a href="{{ route('filter.category',$category->id) }}">
                        <div class="card card-statistic-2">
                        
                            <div class="card-icon shadow-primary bg-primary">
                                <i class="fas fa-archive"></i>
                            </div>
                            <div class="card-wrap">
                                <div class="card-header">
                                    <h4>{{ $category->category_name }}</h4>
                                </div>
                            <div class="card-body">
                              
                            </div>
                            </div>
                        </div>
                    </a>
                    </div>
                @endforeach

                    <div class="col-lg-4 col-md-4 col-sm-12">
                        <a href="{{ route('team') }}">
                            <div class="card card-statistic-2">
                            
                                <div class="card-icon shadow-primary bg-primary">
                                <i class="fas fa-archive"></i>
                                </div>
                                <div class="card-wrap">
                                <div class="card-header">
                                    <h4>Total Team Members</h4>
                                </div>
                                <div class="card-body">
                                    {{ $total_team }}
                                </div>
                                </div>
                            </div>
                        </a>
                    </div>



                    
                   
                </div>
            </section>
        </div>
@endsection