-- Adminer 4.7.8 MySQL dump

SET NAMES utf8;
SET time_zone = '+00:00';
SET foreign_key_checks = 0;
SET sql_mode = 'NO_AUTO_VALUE_ON_ZERO';

SET NAMES utf8mb4;

DROP TABLE IF EXISTS `cache`;
CREATE TABLE `cache` (
  `key` varchar(255) NOT NULL,
  `value` mediumtext NOT NULL,
  `expiration` int(11) NOT NULL,
  PRIMARY KEY (`key`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

INSERT INTO `cache` (`key`, `value`, `expiration`) VALUES
('<EMAIL>|**************',	'i:2;',	1747810768),
('<EMAIL>|**************:timer',	'i:1747810768;',	1747810768);

DROP TABLE IF EXISTS `cache_locks`;
CREATE TABLE `cache_locks` (
  `key` varchar(255) NOT NULL,
  `owner` varchar(255) NOT NULL,
  `expiration` int(11) NOT NULL,
  PRIMARY KEY (`key`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;


DROP TABLE IF EXISTS `categories`;
CREATE TABLE `categories` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `category_name` varchar(255) NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

INSERT INTO `categories` (`id`, `category_name`, `created_at`, `updated_at`) VALUES
(3,	'INVESTIGATIONS',	'2025-05-21 06:50:25',	'2025-05-21 06:50:25'),
(4,	'EXHIBITIONS',	'2025-05-21 06:50:57',	'2025-05-21 06:50:57'),
(5,	'EVENTS',	'2025-05-21 06:51:12',	'2025-05-21 06:51:12'),
(6,	'PUBLICATIONS',	'2025-05-21 06:51:35',	'2025-05-21 06:51:35');

DROP TABLE IF EXISTS `failed_jobs`;
CREATE TABLE `failed_jobs` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `uuid` varchar(255) NOT NULL,
  `connection` text NOT NULL,
  `queue` text NOT NULL,
  `payload` longtext NOT NULL,
  `exception` longtext NOT NULL,
  `failed_at` timestamp NOT NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`id`),
  UNIQUE KEY `failed_jobs_uuid_unique` (`uuid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;


DROP TABLE IF EXISTS `jobs`;
CREATE TABLE `jobs` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `queue` varchar(255) NOT NULL,
  `payload` longtext NOT NULL,
  `attempts` tinyint(3) unsigned NOT NULL,
  `reserved_at` int(10) unsigned DEFAULT NULL,
  `available_at` int(10) unsigned NOT NULL,
  `created_at` int(10) unsigned NOT NULL,
  PRIMARY KEY (`id`),
  KEY `jobs_queue_index` (`queue`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;


DROP TABLE IF EXISTS `job_batches`;
CREATE TABLE `job_batches` (
  `id` varchar(255) NOT NULL,
  `name` varchar(255) NOT NULL,
  `total_jobs` int(11) NOT NULL,
  `pending_jobs` int(11) NOT NULL,
  `failed_jobs` int(11) NOT NULL,
  `failed_job_ids` longtext NOT NULL,
  `options` mediumtext DEFAULT NULL,
  `cancelled_at` int(11) DEFAULT NULL,
  `created_at` int(11) NOT NULL,
  `finished_at` int(11) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;


DROP TABLE IF EXISTS `migrations`;
CREATE TABLE `migrations` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `migration` varchar(255) NOT NULL,
  `batch` int(11) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES
(1,	'0001_01_01_000000_create_users_table',	1),
(2,	'0001_01_01_000001_create_cache_table',	1),
(3,	'0001_01_01_000002_create_jobs_table',	1),
(4,	'2025_05_07_110039_create_posts_table',	1),
(5,	'2025_05_07_115939_create_table_contents_table',	1),
(6,	'2025_05_19_053321_create_site_settings_table',	1),
(7,	'2025_05_19_070925_create_categories_table',	1),
(8,	'2025_05_20_131924_add_image_to_posts_table',	2);

DROP TABLE IF EXISTS `password_reset_tokens`;
CREATE TABLE `password_reset_tokens` (
  `email` varchar(255) NOT NULL,
  `token` varchar(255) NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`email`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;


DROP TABLE IF EXISTS `posts`;
CREATE TABLE `posts` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `title` varchar(255) NOT NULL,
  `publish_date` varchar(255) DEFAULT NULL,
  `type` varchar(255) DEFAULT NULL,
  `details` longtext DEFAULT NULL,
  `image` varchar(255) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

INSERT INTO `posts` (`id`, `title`, `publish_date`, `type`, `details`, `image`, `created_at`, `updated_at`) VALUES
(4,	'ENVIRONMENTAL INVESTIGATIONS',	'2018-01-02',	'5',	'<p>This workshop explored spatial and media practice as tools for visualizing and bringing to the foreground specific environment related conflicts, inequalities and disputes. Participants made tactical use of concepts such as scale, territory, forensics, before and after, landscape and diagram. We grounded practice and theory with the methodology afforded by environmental history at the intersection of nature, capital and social relations. Participants worked collectively on contemporary situations and cases that are specific to and resonates with the ecology and climate of Bangladesh and the wider Bengal delta, to explore the process of gathering, examining and presenting environmental investigations.</p>\r\n<p>Partners:</p>\r\n<p><a href=\"http://www.dhakaartsummit.org/new-page-1\" target=\"_blank\" rel=\"noopener\">www.dhakaartsummit.org/new-page-1</a></p>',	'https://phpstack-1312572-5299041.cloudwaysapps.com//storage/files/3/682d775d81510.jpg',	'2025-05-21 06:49:27',	'2025-05-21 07:15:48'),
(5,	'Race and Forest 2.0',	NULL,	'4',	'<p>Second expanded edition of Race and Forest exhibition took place in Biennale Warszawa getting back the reconstruction of Polish charge against nine Germans for the devastation of the Polish forests presented in UNWCC case no. 1307/7150. Second edition also included new video work presenting the site analysis of Chełmno nad Nerem death camp, where nature was used in the attempt to hide evidence of the genocide. In the context of COVID-19 pandemic exhibition was also presented in the online format.</p>',	'https://phpstack-1312572-5299041.cloudwaysapps.com//storage/files/3/682d7e86f08e4.jpg',	'2025-05-21 07:21:30',	'2025-05-21 07:21:30'),
(6,	'RED MUD: ALTEO’S LICENSE TO POLLUTE THE MEDITERRANEAN SEA',	NULL,	'3',	'<p>Evidence file for ecocide mock trial on industrial pollution in the Mediterranean Sea</p>\r\n<p>&nbsp;</p>\r\n<p>From 1967 to 2015, ALTEO-Rio Tinto dumped more than 30 million tons of toxic red mud into the Mediterranean Sea. In 2016 ALTEO stopped discharging solid waste into the Mediterranean. However, liquid effluent containing toxic metals continues to be released into the sea until today. The long-term smothering of the seafloor by the release of the red mud has had severe impacts on the local ecosystems. Loss of cold-water coral reefs and contamination of fish habitats at multiple depths of seabed led to depletion and contamination of fish stock, resulting in a loss of livelihood for local fishermen.</p>\r\n<p>&nbsp;</p>\r\n<p>INTERPRT produced an evidence file for an ecocide moot court and mock trial organized by Wild Legal that took place on 26 June, 2021. It was set to challenge existing environment protection laws and whether these actions could constitute a major case of ecocide in French law. Our visual investigation focused on the Cassidaigne canyon in the Calanques National Park, where the red mud is discharged at a depth of 324 meters, covering a 900 square km wide area on the seabed. We synthesized publicly available environmental data into a seamless 3D model of the canyon and a multimedia presentation.</p>',	'https://phpstack-1312572-5299041.cloudwaysapps.com//storage/files/3/682d95c1dd16d.jpg',	'2025-05-21 09:03:10',	'2025-05-21 09:03:10'),
(8,	'MORUROA FILES',	NULL,	'3',	'<p>Online platform on exposing the toxic history of French nuclear tests in the Mao&rsquo;hi Nui (French Polynesia)</p>\r\n<p>&nbsp;</p>\r\n<p>France conducted 193 nuclear weapons tests between 1966 and 1996, at Moruroa and Fangataufa atolls in French Polynesia/Te Ao Mā&rsquo;ohi. Between 1966 and 1974, 41 nuclear tests were conducted in the atmosphere, and between 1975 and 1996, 152 underground tests were detonated at the two test sites. The fallout from the atmospheric tests alone exposed the local population, workers at the test sites and French soldiers to significant levels of ionizing radiation.</p>\r\n<p>&nbsp;</p>\r\n<p>INTERPRT, Disclose and Princeton University&rsquo;s Program on Science &amp; Global Security (SGS) have developed an interactive platform to shed new light on French nuclear tests conducted in French Polynesia. The platform, for the first time, reconstructs key atmospheric nuclear tests, their fallouts and potential radiation exposure of local populations. Its source material is drawn from exhaustive analysis of around two thousand pages of recently declassified French Ministry of Defense documents, historical maps, photographs, testimonies, interviews with victims, veterans, state officials and civil society organizations in both French Polynesia and in France.</p>\r\n<p>&nbsp;</p>\r\n<p>The platform&rsquo;s core consists of investigations into three important atmospheric tests: Aldebaran (2 July 1966), Encelade (12 June 1971) and Centaure (17 July 1974) using 3D models, interactive maps, animations, and photographs that takes the user to the scenes of complex nuclear events and their aftermath.</p>',	'https://phpstack-1312572-5299041.cloudwaysapps.com//storage/files/3/682d97a8c8910.jpg',	'2025-05-21 09:13:05',	'2025-05-21 09:13:05'),
(9,	'BLUE PERIL SCREENING AT 2022 UNITED NATIONS OCEAN CONFERENCE SIDE EVENT',	NULL,	'5',	'<p>INTERPRT&rsquo;s visual investigation of deep sea mining in the Pacific, Blue Peril, was screened at the 2022 United Nations Ocean Conference side event.</p>',	NULL,	'2025-05-21 09:35:40',	'2025-05-21 09:35:40'),
(10,	'MINING THE ABYSS',	NULL,	'3',	'<p>Predicting the impacts of Deep-Sea Mining in the Pacific Ocean</p>\r\n<p>&nbsp;</p>\r\n<p>Mining the Abyss is a visual investigation on deep-sea mining and accountability.</p>\r\n<p>&nbsp;</p>\r\n<p>A speculative rush is underway to exploit deep seabed minerals &ndash; cobalt, nickel, copper and manganese &ndash; allegedly needed for the green shift, led by the International Seabed Authority (ISA), a handful of states, mining startups, frontier investors and research universities.&nbsp;</p>\r\n<p>&nbsp;</p>\r\n<p>The deep ocean poses formidable challenges for its spatial and visual representation, which the mining industry exploits by claiming its mining operations will be environmentally friendly. To counter the mining industry&rsquo;s greenwashing, INTERPRT analyzed data shared by marine biologists to simulate mining footprints and then collaborated with an oceanographer to model the trajectory of plume particles from seabed mining in the CCZ. INTERPRT designed the results using 3D modeling software to create a multimedia advocacy tool.</p>\r\n<p>&nbsp;</p>\r\n<p>Blue Peril &ndash; our advocacy video &ndash; produced in collaboration with Deep Sea Mining Campaign and Ozeanien Dialog in cooperation with Pacific civil society organizations fighting to stop deep sea mining, was launched in an official side event of the 2022 UN Ocean Conference in Lisbon.</p>\r\n<p>&nbsp;</p>\r\n<p>Blue Peril presents a scientifically robust and disturbing picture of far-reaching future impacts of deep-sea mining for Pacific Ocean ecosystems, habitat and Pacific Island communities. It highlights the serious implications for Pacific Island economies and way of life &ndash; with Hawaii and Kiribati predicted to be in the firing line. Focusing on the Tonga and Nauru sponsored license areas of The Metals Company (TMC) in the Clarion-Clipperton Zone (CCZ), Blue Peril incorporates the best publicly available data into internationally accredited oceanographic and spatial imagery programs. Blue Peril is accompanied by technical notes.</p>\r\n<p>&nbsp;</p>\r\n<p>Mining the abyss demonstrates &ndash; for the first time &ndash; the vast area of the Pacific expected to be impacted by deep-sea mining and the devastation that deep seabed mining could bring to marine ecosystems and habitats.</p>',	'https://phpstack-1312572-5299041.cloudwaysapps.com//storage/files/3/682d9edb0438c.jpg',	'2025-05-21 09:39:16',	'2025-05-21 09:39:16'),
(11,	'TRACER STUDIES',	NULL,	'4',	'<p>The deep ocean poses formidable challenges for its spatial and visual representation. Around 5000 meters below, mining vehicles plowing through the soft sediment of the seabed will produce vast pollution trails. As part of its investigation, INTERPRT modeled sediment plume from DSM in a key site within the CCZ using OpenDrift, an open source code for predicting how ocean currents transport particles and objects such as oil drifts and microplastics. Drifting far outside the mining areas, INTERPRT&rsquo;s &ldquo;tracer studies&rdquo; show how pollution trails would travel far beyond the mining area and potentially impact seafloor life along their trajectory.</p>',	'https://phpstack-1312572-5299041.cloudwaysapps.com//storage/files/3/682da01798e3d.jpg',	'2025-05-21 09:43:31',	'2025-05-21 09:43:31'),
(12,	'TOWARDS A NEW INTERNATIONAL CRIME OF ECOCIDE?',	NULL,	'5',	'<p><span style=\"color: #1f1f1f; font-family: Gothic A1, sans-serif;\"><span style=\"font-size: 21px; letter-spacing: 0.21px;\">INTERPRT&rsquo;s Nabil Ahemd took part in the event &lsquo;Towards A New International Crime Of Ecocide?&rsquo; in Oslo, Norway, along with Dr Christina Voigt, Professor of Law at the University of Oslo, and Jojo Mehta, Co-Founder and Executive Director, Stop Ecocide International. The discussion addressed the ecocide definition, the International Criminal Court route, nations that are already leading in this conversation, and how to get Norway on board with supporting the effort.</span></span></p>',	NULL,	'2025-05-21 09:47:46',	'2025-05-21 09:47:58'),
(13,	'BRAZIL: ATROCITY CRIMES IN THE AMAZON',	NULL,	'3',	'<p>Evidence Platform supporting Communication submitted to the Office of the Prosecutor (OTP) of the ICC</p>\r\n<p>&nbsp;</p>\r\n<p>In the Brazilian Amazon rainforest, mass atrocity crimes have been associated with the dispossession of land, the exploitation of natural resources, and the destruction of its environment, while enabling and entrenching the widespread dispossession of Rural Land Users.</p>\r\n<p>&nbsp;</p>\r\n<p>INTERPRT created an Evidence Platform to support an Article 15 Communication submitted to the Office of the Prosecutor of the International Criminal Court by Climate Counsel, Greenpeace Brazil, Observat&oacute;rio do Clima, and Greenpeace International. The Communication was filed on behalf of Rural Land Users and Defenders who are victims of the alleged crimes against humanity.&nbsp;</p>\r\n<p>&nbsp;</p>\r\n<p>This platform supports the ICC communication, by bringing together disparate forms of evidence, photographs, testimonies from survivors and family members of victims, 3D reconstruction of crime scenes, forensic evidence, visualizations of data collected by Comiss&atilde;o Pastoral da Terra (CPT) between 2011 and 2021, drone videos, satellite imagery analysis, deforestation and forest fire data on the Amazon. Together they demonstrate crimes against thousands of rural land users and defenders in Brazil have intensified under President Jair Bolsonaro&rsquo;s administration.</p>',	'https://phpstack-1312572-5299041.cloudwaysapps.com//storage/files/3/682da62c013e4.jpg',	'2025-05-21 10:10:47',	'2025-05-21 10:10:47'),
(14,	'MINING THE ABYSS',	NULL,	'4',	'<p>The deep ocean poses formidable challenges for its spatial and visual representation, which the mining industry exploits by claiming its mining operations will be environmentally friendly. To counter the mining industry&rsquo;s greenwashing, INTERPRT analyzed data shared by marine biologists to simulate mining footprints and then collaborated with an oceanographer to model the trajectory of plume particles from seabed mining in the CCZ. INTERPRT designed the results using 3D modeling software to create a multimedia advocacy tool.&nbsp;</p>\r\n<p>&nbsp;</p>\r\n<p>The exhibition presents Blue Peril &ndash; our advocacy video on deep-sea mining and explores some of our methodology and research behind it.&nbsp; Blue Peril &ndash; demonstrates, for the first time, the vast area of the Pacific expected to be impacted by deep-sea mining and the devastation that deep seabed mining could bring to marine ecosystems and habitats.</p>',	'https://phpstack-1312572-5299041.cloudwaysapps.com//storage/files/3/682da6db64741.jpg',	'2025-05-21 10:12:20',	'2025-05-21 10:18:31'),
(15,	'COLONIAL PRESENT: COUNTER-MAPPING THE TRUTH AND RECONCILIATION COMMISSIONS IN SÁPMI',	NULL,	'4',	'<p>The S&aacute;mi have long desired for a public process and an engagement to examine and expose the Nordic states&rsquo; colonial, assimilationist policies toward the S&aacute;mi people. (1)</p>\r\n<p>&nbsp;</p>\r\n<p>In 2018 Norway established &ldquo;The commission to investigate the Norwegianisation policy and injustice against the S&aacute;mi and Kven/Norwegian Finnish peoples&rdquo;. In 2021, both Finland and Sweden moved forward on truth and reconciliation commissions of their own to document historical violations and their contemporary consequences on the S&aacute;mi. Yet public knowledge about the truth commissions in Norway, Finland and Sweden are lacking.</p>\r\n<p>&nbsp;</p>\r\n<p>Critics of the commissions doubt the intention of Nordic governments for reconciliation when current policies continue to violate&nbsp; rights, for example by promoting both extractive projects and &ldquo;green colonialism&rdquo; in the form of harmful sustainability projects which could destroy traditional ways of life already under threat from the climate emergency.&nbsp;&nbsp;</p>\r\n<p>&nbsp;</p>\r\n<p>Just consider the February 2023 protests in Oslo, where young S&aacute;mi protesters had to barricade government buildings to get the Norwegian state to listen to their demands for the enforcement of a Supreme Court ruling 500 days after they found the construction of industrial wind farms in Fosen illegal.&nbsp;</p>\r\n<p>&nbsp;</p>\r\n<p>How can past injustices be reconciled within the ongoing structures of colonialism? To address this question, we interviewed and met with S&aacute;mi activists, archeologists, historians, journalists, and researchers and examined cartographic evidence, photographs and other archival sources.&nbsp;</p>\r\n<p>&nbsp;</p>\r\n<p>It is while working on the research for this project that we became part of a legal action between S&aacute;mi herders of the Jillen-Njaarke reindeer herding district and &Oslash;yfjellet wind farm in Norway. We conducted a series of participatory workshops with herders and collected environmental data combined with the herders&rsquo; own videos, to create a 3D environment of the disputed site, towards counter-mapping the colonial present in S&aacute;pmi.</p>\r\n<p>&nbsp;</p>\r\n<p>(1) Rauna Kuokkanen in Human Rights Review (2020)</p>',	'https://phpstack-1312572-5299041.cloudwaysapps.com//storage/files/3/682da83e347f4.jpg',	'2025-05-21 10:18:16',	'2025-05-21 10:18:16');

DROP TABLE IF EXISTS `sessions`;
CREATE TABLE `sessions` (
  `id` varchar(255) NOT NULL,
  `user_id` bigint(20) unsigned DEFAULT NULL,
  `ip_address` varchar(45) DEFAULT NULL,
  `user_agent` text DEFAULT NULL,
  `payload` longtext NOT NULL,
  `last_activity` int(11) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `sessions_user_id_index` (`user_id`),
  KEY `sessions_last_activity_index` (`last_activity`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

INSERT INTO `sessions` (`id`, `user_id`, `ip_address`, `user_agent`, `payload`, `last_activity`) VALUES
('ADlswXQdoT3KRlyFHQqEcCuV6d7EptQgHjrb2IB0',	3,	'**************',	'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',	'YTo0OntzOjY6Il90b2tlbiI7czo0MDoiZnFVMWlQY0p4cGlpOWFRR0VCQTJJT016OGN4dnk4RWVDMlY5dUY2ZyI7czo5OiJfcHJldmlvdXMiO2E6MTp7czozOiJ1cmwiO3M6NjM6Imh0dHBzOi8vcGhwc3RhY2stMTMxMjU3Mi01Mjk5MDQxLmNsb3Vkd2F5c2FwcHMuY29tL2FwaS9pbnNpZ2h0cyI7fXM6NjoiX2ZsYXNoIjthOjI6e3M6Mzoib2xkIjthOjA6e31zOjM6Im5ldyI7YTowOnt9fXM6NTA6ImxvZ2luX3dlYl81OWJhMzZhZGRjMmIyZjk0MDE1ODBmMDE0YzdmNThlYTRlMzA5ODlkIjtpOjM7fQ==',	1747822717),
('U0qoga1RqN9CWaNrCTQNBF7auNVl9zsqD07xwpT9',	2,	'**************',	'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',	'YTozOntzOjY6Il90b2tlbiI7czo0MDoieGM2NXNTWTVZRzhPaUlMUHpETE5kbmp2VmJGQ0YwT053WndXZUdsdiI7czo5OiJfcHJldmlvdXMiO2E6MTp7czozOiJ1cmwiO3M6NjM6Imh0dHBzOi8vcGhwc3RhY2stMTMxMjU3Mi01Mjk5MDQxLmNsb3Vkd2F5c2FwcHMuY29tL2FwaS9pbnNpZ2h0cyI7fXM6NjoiX2ZsYXNoIjthOjI6e3M6Mzoib2xkIjthOjA6e31zOjM6Im5ldyI7YTowOnt9fX0=',	1747833004);

DROP TABLE IF EXISTS `site_settings`;
CREATE TABLE `site_settings` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `logo` varchar(255) NOT NULL,
  `meta_title` varchar(255) NOT NULL,
  `meta_description` text NOT NULL,
  `meta_author` varchar(255) NOT NULL,
  `meta_keywords` varchar(255) NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;


DROP TABLE IF EXISTS `table_contents`;
CREATE TABLE `table_contents` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `field_name` longtext NOT NULL,
  `value` longtext NOT NULL,
  `post_id` int(11) NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

INSERT INTO `table_contents` (`id`, `field_name`, `value`, `post_id`, `created_at`, `updated_at`) VALUES
(13,	'DATE',	'02/01/2018',	4,	'2025-05-21 07:15:48',	'2025-05-21 07:15:48'),
(14,	'VENUE',	'Dhaka Art Summit, Dhaka',	4,	'2025-05-21 07:15:48',	'2025-05-21 07:15:48'),
(15,	'DATE',	'2020',	5,	'2025-05-21 07:21:30',	'2025-05-21 07:21:30'),
(16,	'VENUE',	'Biennale Warszawa',	5,	'2025-05-21 07:21:30',	'2025-05-21 07:21:30'),
(17,	'SITUATION',	'Concluded',	6,	'2025-05-21 09:03:10',	'2025-05-21 09:03:10'),
(18,	'PERIOD',	'1967 – ongoing',	6,	'2025-05-21 09:03:10',	'2025-05-21 09:03:10'),
(19,	'LOCATION',	'Mediterranean Sea',	6,	'2025-05-21 09:03:10',	'2025-05-21 09:03:10'),
(20,	'METHODOLOGY',	'3D Modeling, Testimony',	6,	'2025-05-21 09:03:10',	'2025-05-21 09:03:10'),
(21,	'PARTNERS',	'Wild Legal',	6,	'2025-05-21 09:03:10',	'2025-05-21 09:03:10'),
(22,	'PROJECT TEAM',	'Lead investigators: Nabil Ahmed and Olga Lucko Research: Tiago Patatas',	6,	'2025-05-21 09:03:10',	'2025-05-21 09:03:10'),
(23,	'Keywords',	'Ecocide , Mediterranean Sea',	6,	'2025-05-21 09:03:10',	'2025-05-21 09:03:10'),
(29,	'SITUATION',	'Concluded',	8,	'2025-05-21 09:13:05',	'2025-05-21 09:13:05'),
(30,	'PERIOD',	'1966 – 1996',	8,	'2025-05-21 09:13:05',	'2025-05-21 09:13:05'),
(31,	'LOCATION',	'Mao’hi Nui (French Polynesia)',	8,	'2025-05-21 09:13:05',	'2025-05-21 09:13:05'),
(32,	'METHODOLOGY',	'3D Modeling, Atmospheric Transport Modeling',	8,	'2025-05-21 09:13:05',	'2025-05-21 09:13:05'),
(33,	'PARTNERS',	'Disclose, Princeton University’s Program on Science and Global Security (SGS)',	8,	'2025-05-21 09:13:05',	'2025-05-21 09:13:05'),
(34,	'PROJECT TEAM',	'Co-PI: Nabil Ahmed (INTERPRT/NTNU), Geoffrey Livolsi (Disclose), Sébastien Philippe (SGS) Interprt: Nabil Ahmed (Co-PI), Olga Lucko (lead architectural researcher and web design), Svitlana Lavrenchuk (architectural researcher, 3D design and animation), Filip Wesołowski (animation production), Martinus Suijkerbuijk (document OCR) Platform development: Code Chorus (James Dose and Jacob Liu) Disclose: Mathias Destal (editor-in-chief), Geoffrey Livolsi (Co-PI), Tomas Statius (investigative journalist), Mathieu Asselin (photographer) Program on Science and Global Security - Princeton University: Sébastien Philippe (Co-PI, research and scientific modelisation)',	8,	'2025-05-21 09:13:05',	'2025-05-21 09:13:05'),
(35,	'COMMISIONED BY',	'00',	8,	'2025-05-21 09:13:05',	'2025-05-21 09:13:05'),
(36,	'Keywords',	'French nuclear tests , Moruroa',	8,	'2025-05-21 09:13:05',	'2025-05-21 09:13:05'),
(37,	'DATE',	'06/30/2022',	9,	'2025-05-21 09:35:40',	'2025-05-21 09:35:40'),
(38,	'VENUE',	'United Nations Ocean Conference, Lisbon',	9,	'2025-05-21 09:35:40',	'2025-05-21 09:35:40'),
(39,	'SITUATION',	'Concluded',	10,	'2025-05-21 09:39:16',	'2025-05-21 09:39:16'),
(40,	'PERIOD',	'2001 – ongoing',	10,	'2025-05-21 09:39:16',	'2025-05-21 09:39:16'),
(41,	'LOCATION',	'Pacific Ocean',	10,	'2025-05-21 09:39:16',	'2025-05-21 09:39:16'),
(42,	'METHODOLOGY',	'3D Modeling, Plume simulation, Testimony',	10,	'2025-05-21 09:39:16',	'2025-05-21 09:39:16'),
(43,	'PARTNERS',	'Deep Sea Mining Campaign, Ozeanien-Dialog',	10,	'2025-05-21 09:39:16',	'2025-05-21 09:39:16'),
(44,	'PROJECT TEAM',	'Lead investigators: Nabil Ahmed and Olga Lucko 3D modeling: Olga Lucko, Gwil Hughes, Einar Grinde, Svitlana Lavrenchuk, Martinus Suijkerbuijk Motion Graphics: Filip Wesołowski, Gwil Hughes',	10,	'2025-05-21 09:39:16',	'2025-05-21 09:39:16'),
(45,	'Keywords',	'Deep Sea Mining',	10,	'2025-05-21 09:39:16',	'2025-05-21 09:39:16'),
(46,	'DATE',	'2022',	11,	'2025-05-21 09:43:31',	'2025-05-21 09:43:31'),
(47,	'VENUE',	'Deutsches Technikmuseum, Berlin',	11,	'2025-05-21 09:43:31',	'2025-05-21 09:43:31'),
(51,	'DATE',	'09/26/2022',	12,	'2025-05-21 09:47:58',	'2025-05-21 09:47:58'),
(52,	'VENUE',	'Domus Juridica, Oslo',	12,	'2025-05-21 09:47:58',	'2025-05-21 09:47:58'),
(53,	'PARTNERS',	'Stop Ecocide International',	12,	'2025-05-21 09:47:58',	'2025-05-21 09:47:58'),
(54,	'SITUATION',	'Concluded',	13,	'2025-05-21 10:10:47',	'2025-05-21 10:10:47'),
(55,	'PERIOD',	'2011 – 2021',	13,	'2025-05-21 10:10:47',	'2025-05-21 10:10:47'),
(56,	'LOCATION',	'Brazilian Amazon',	13,	'2025-05-21 10:10:47',	'2025-05-21 10:10:47'),
(57,	'METHODOLOGY',	'3D Modelling, Testimony, Ground Truth',	13,	'2025-05-21 10:10:47',	'2025-05-21 10:10:47'),
(58,	'PARTNERS',	'Climate Counsel, Greenpeace Brazil, Observatório do Clima, Greenpeace International.',	13,	'2025-05-21 10:10:47',	'2025-05-21 10:10:47'),
(59,	'PROJECT TEAM',	'Lead investigators: Nabil Ahmed and Olga Lucko Lead architectural researcher: Olga Lucko Architectural researcher: Tiago Patatas Video editor: Prerna Bishnoi 3D modeling: Eduardo Paranhos and Einar Grinde (Baiao case) Researcher: Raquel Pais (Pau d’arco case)',	13,	'2025-05-21 10:10:47',	'2025-05-21 10:10:47'),
(60,	'COMMISIONED BY',	'Climate Counsel',	13,	'2025-05-21 10:10:47',	'2025-05-21 10:10:47'),
(61,	'Keywords',	'Crimes Against Humanity , Ecocide , International Criminal Court',	13,	'2025-05-21 10:10:47',	'2025-05-21 10:10:47'),
(65,	'DATE',	'2023',	15,	'2025-05-21 10:18:16',	'2025-05-21 10:18:16'),
(66,	'VENUE',	'Helsinki Biennial',	15,	'2025-05-21 10:18:16',	'2025-05-21 10:18:16'),
(67,	'DATE',	'2022',	14,	'2025-05-21 10:18:31',	'2025-05-21 10:18:31'),
(68,	'VENUE',	'Istanbul Biennale, Istanbul',	14,	'2025-05-21 10:18:31',	'2025-05-21 10:18:31');

DROP TABLE IF EXISTS `users`;
CREATE TABLE `users` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(255) NOT NULL,
  `email` varchar(255) NOT NULL,
  `email_verified_at` timestamp NULL DEFAULT NULL,
  `password` varchar(255) NOT NULL,
  `remember_token` varchar(100) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `users_email_unique` (`email`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

INSERT INTO `users` (`id`, `name`, `email`, `email_verified_at`, `password`, `remember_token`, `created_at`, `updated_at`) VALUES
(1,	'Test User',	'<EMAIL>',	'2025-05-20 06:36:10',	'$2y$12$s/Rem94Bgu.rspAkhlgZA.5Irg3kdwNOVjFfFyYo4FvygY7bCXDFS',	'yaQ3UWr1id',	'2025-05-20 06:36:10',	'2025-05-20 06:36:10'),
(2,	'shakib',	'<EMAIL>',	NULL,	'$2y$12$K8T2Aef0R0WTFhtriQiru.dOEPNWxK1IOkWinWzHZ/sjaM7R1OBXm',	'LD8G8vfwKjh0jZwKuZ10rdi1SU64llWAvaW8YY5UqGURmq2R087uGhdekkY6',	'2025-05-20 06:38:49',	'2025-05-20 06:38:49'),
(3,	'james.dose',	'<EMAIL>',	NULL,	'$2y$12$at/JO1fdvRF/sXtcZ.cWgOfNGckFQqyFc7VoQd1zqG5A71CvZwTZy',	NULL,	'2025-05-21 06:44:02',	'2025-05-21 06:44:02'),
(4,	'Shifat E Rasul',	'<EMAIL>',	NULL,	'$2y$12$YqwhykK1prb/J6/D7fWGJ.R9L1cSLEYmsEUlWPl/dhaojKElNY7/6',	NULL,	'2025-05-21 06:59:03',	'2025-05-21 06:59:03');

-- 2025-05-21 13:24:59