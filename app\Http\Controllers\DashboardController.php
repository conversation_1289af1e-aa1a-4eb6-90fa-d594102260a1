<?php

namespace App\Http\Controllers;
use App\Models\posts;
use App\Models\category;
use App\Models\team;
use Illuminate\Http\Request;

class DashboardController extends Controller
{
    //
    public function index(){
         $total_posts=posts::count();
         $total_category=category::count();
         $total_team=team::count();
         $categories=Category::orderBy('id','ASC')->get();
         return view('backend.index',compact('total_posts','total_category','total_team','categories'));
    }
}
