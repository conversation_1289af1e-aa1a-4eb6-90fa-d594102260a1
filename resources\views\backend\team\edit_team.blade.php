@extends('backend.master')
@section('main')
        <!-- Start app main Content -->
        <div class="main-content">
        <section class="section">
                
                <div class="section-body">
                  
                 
                  
                    <div class="row">
                        <div class="col-12">
                            <div class="card">
                                <div class="card-header">
                                    <h4>Edit Team</h4>
                                </div>
                                <div class="card-body">
                                    <form action="{{ route('team.update') }}" method="POST">
                                        @csrf
                                        <input type="hidden" name="id" value="{{ $team->id }}" class="form-control">
                                    <div class="form-group row mb-4">
                                        <label class="col-form-label text-md-right col-12 col-md-3 col-lg-3">Name</label>
                                        <div class="col-sm-12 col-md-7">
                                            <input type="text" name="name" value="{{ $team->name }}" class="form-control">
                                        </div>
                                    </div>

                                    <div class="form-group row mb-4">
                                        <label class="col-form-label text-md-right col-12 col-md-3 col-lg-3">Position</label>
                                        <div class="col-sm-12 col-md-7">
                                            <input type="text" name="position" value="{{$team->position}}" class="form-control">
                                        </div>
                                    </div>



                                 <div class="form-group row mb-4">
                                    <label class="col-form-label text-md-right col-12 col-md-3 col-lg-3">Details</label>
                                    <div class="col-sm-12 col-md-7">
                                        <textarea class="summernote" name="details">{{ old('details', $team->details) }}</textarea>
                                        
                                    </div>
                                </div>

                                    <div class="form-group row mb-4">
                                        <label class="col-form-label text-md-right col-12 col-md-3 col-lg-3">Select Team</label>
                                        <div class="col-sm-12 col-md-7">
                                            <select class="form-control selectric" name="category_name">
                                            <option value="{{$team->category_name}}">{{$team->category_name}}</option>
                                             @foreach($team_category as $team_category)
                                                    <option value="{{ $team_category->category_name }}">{{ $team_category->category_name }}</option>
                                                @endforeach
                                               
                                            </select>
                                        </div>
                                    </div>
                                    <div class="form-group row mb-4">
                                        <label class="col-form-label text-md-right col-12 col-md-3 col-lg-3"></label>
                                        <div class="col-sm-12 col-md-7">
                                        <input type="submit" class="btn btn-primary px-4" value="Save Changes" />
                                        </div>
                                    </div>
                                </form>
                                </div>
                            </div>
                        </div>
                    </div>
                   
                                    </div>
                                   
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </section>
        </div>


        <!-- Scripts -->
<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>

<script>
    function initSummernote(selector) {
        $(selector).summernote({
            height: 250,
            callbacks: {
                onImageUpload: function(files) {
                    sendFile(files[0], $(this));
                },
                onInit: function() {
                    let toolbar = $(this).next('.note-editor').find('.note-toolbar');
                    toolbar.append(`
                        <button type="button" class="btn btn-sm btn-light btn-lfm" data-type="Images">
                            <i class="note-icon-picture"></i> Browse
                        </button>
                    `);
                }
            }
        });
    }

    function sendFile(file, $editor) {
        var data = new FormData();
        data.append("upload", file);
        data.append("_token", $('meta[name="csrf-token"]').attr('content'));

        $.ajax({
            url: "/laravel-filemanager/upload?type=Images",
            method: "POST",
            data: data,
            contentType: false,
            processData: false,
            success: function(response) {
                if (response.url) {
                    $editor.summernote('insertImage', response.url);
                } else {
                    alert("Upload failed");
                }
            },
            error: function(jqXHR) {
                console.error("Upload error:", jqXHR);
                alert("Upload error: " + jqXHR.statusText);
            }
        });
    }

    function openLfm(callback, type = 'Images') {
        window.open('/laravel-filemanager?type=' + type, 'FileManager', 'width=900,height=600');
        window.SetUrl = callback;
    }





    $(document).ready(function() {
        const APP_URL = "{{ url('/') }}";

        function initSummernote(selector) {
            $(selector).summernote({
                height: 250,
                callbacks: {
                    onImageUpload: function(files) {
                        sendFile(files[0], $(this));
                    },
                    onInit: function() {
                        let toolbar = $(this).next('.note-editor').find('.note-toolbar');
                        toolbar.append(`
                        <button type="button" class="btn btn-sm btn-light btn-lfm" data-type="Images">
                            <i class="note-icon-picture"></i> Browse
                        </button>
                    `);
                    }
                }
            });
        }

        function sendFile(file, $editor) {
            var data = new FormData();
            data.append("upload", file);
            data.append("_token", $('meta[name="csrf-token"]').attr('content'));

            $.ajax({
                url: "/laravel-filemanager/upload?type=Images",
                method: "POST",
                data: data,
                contentType: false,
                processData: false,
                success: function(response) {
                    if (response.url) {
                        $editor.summernote('insertImage', response.url);
                    } else {
                        alert("Upload failed");
                    }
                },
                error: function(jqXHR) {
                    alert("Upload error: " + jqXHR.statusText);
                }
            });
        }

        function openLfm(callback, type = 'Images') {
            window.open('/laravel-filemanager?type=' + type, 'FileManager', 'width=900,height=600');
            window.SetUrl = callback;
        }

        function lfm(id, type, options) {
            let button = document.getElementById(id);

            button.addEventListener('click', function() {
                var route_prefix = (options && options.prefix) ? options.prefix : '/laravel-filemanager';
                var target_input = document.getElementById(button.getAttribute('data-input'));
                var target_preview = document.getElementById(button.getAttribute('data-preview'));

                window.open(route_prefix + '?type=' + (options.type || 'file'), 'FileManager', 'width=900,height=600');

                window.SetUrl = function(items) {
                    var file_path = items.map(item => item.url).join(',');
                    target_input.value = file_path;
                    target_input.dispatchEvent(new Event('change'));

                    target_preview.innerHTML = '';
                    items.forEach(item => {
                        let img = document.createElement('img');
                        img.setAttribute('style', 'height: 5rem');
                        img.setAttribute('src', item.thumb_url);
                        target_preview.appendChild(img);
                    });
                    target_preview.dispatchEvent(new Event('change'));
                };
            });
        }

        // ✅ Initialize Summernote
        $('.summernote').each(function() {
            initSummernote(this);
        });

        // ✅ Add new repeater block
        $('body').on('click', '.btn-increment', function() {
            let html = $('.clone').html();
            let newElement = $(html);
            $('.image-repeater-wrapper').append(newElement);
            newElement.find('.summernote').each(function() {
                // initSummernote(this);
            });
        });

        // ✅ Remove repeater block
        $('body').on('click', '.remove-btn', function() {
            $(this).closest('.control-group').remove();
        });

        // ✅ Handle image upload to Summernote via "Browse"
        $('body').on('click', '.btn-lfm', function() {
            let $note = $(this).closest('.note-editor').prev('.summernote');
            openLfm(function(urls) {
                if (typeof urls === 'string') {
                    $note.summernote('insertImage', urls);
                } else {
                    urls.forEach(function(url) {
                        $note.summernote('insertImage', url.url);
                    });
                }
            }, 'Images');
        });

        // ✅ Initialize LFM picker button
        lfm('lfm', 'image', {
            prefix: '/laravel-filemanager'
        });
    });
</script>


@endsection