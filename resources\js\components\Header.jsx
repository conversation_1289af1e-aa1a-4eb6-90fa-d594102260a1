import React, { useEffect } from 'react';
import { Link, useLocation } from 'react-router-dom'; // Add this import

const Header = ({ activeFilterItem, setActiveFilterItem }) => {
    const location = useLocation();
    // Get the last part of the path as the page name
    const pageName = location.pathname === '/' ? 'home' : location.pathname.split('/').filter(Boolean).pop();

    const handleFilterButtonClick = (filter) => {
        setActiveFilterItem(filter);
        document.querySelectorAll('.single-insight.active').forEach(function(el) {
            const content = el.querySelector('.insight-content');
            if (content) {
                content.classList.remove('active');
                content.style.height = '0';
            }
            el.classList.remove('active');
        });
        // Remove comp-header_active__nxkGN from all header containers
        document.querySelectorAll('.header-flyout-menu_flyout_menu_container__tcC_d').forEach(headerContainer => {
            headerContainer.classList.remove('header-flyout-menu_is_active__NQ4_G');
        });
    };

    useEffect(() => {
        document.querySelectorAll('.header-flyout-menu_flyout_menu_container__tcC_d').forEach(menu => {
            menu.classList.remove('header-flyout-menu_is_active__NQ4_G');
        });

    }
    , [pageName]);


    return (
        <header className={`content ${pageName}`} itemType="https://schema.org/WPHeader">
            <div
                className="comp-header_main_header__G_1aX"
                style={{
                    transform: 'translate3d(0px, 0px, 0px)',
                    transition: 'transform 500ms ease-in-out',
                }}
            >
                <div className="flex-content-block side-gutters container">
                    <div className="comp-header_main_header_container__fB_W0">
                        <div className="blank-header-div"></div>
                        {/* Filter radio group START */}
                        <div className="filter-insights header">
                            <div className="input-radio-wrapper">
                                <ul>
                                    <li className={activeFilterItem === 'all' ? 'active' : ''}>
                                        <input
                                            type="radio"
                                            name="filter"
                                            id="all"
                                            value="all"
                                            onChange={() => handleFilterButtonClick('all')}
                                            checked={activeFilterItem === 'all'}
                                        />
                                        <label htmlFor="all">All projects</label>
                                    </li>
                                    <li className={activeFilterItem === 'investigations' ? 'active' : ''}>
                                        <input
                                            type="radio"
                                            name="filter"
                                            id="investigations"
                                            value="investigations"
                                            onChange={() => handleFilterButtonClick('investigations')}
                                            checked={activeFilterItem === 'investigations'}
                                        />
                                        <label htmlFor="investigations">Investigations</label>
                                    </li>
                                    <li className={activeFilterItem === 'exhibitions' ? 'active' : ''}>
                                        <input
                                            type="radio"
                                            name="filter"
                                            id="exhibitions"
                                            value="exhibitions"
                                            onChange={() => handleFilterButtonClick('exhibitions')}
                                            checked={activeFilterItem === 'exhibitions'}
                                        />
                                        <label htmlFor="exhibitions">Exhibitions</label>
                                    </li>
                                    <li className={activeFilterItem === 'events' ? 'active' : ''}>
                                        <input
                                            type="radio"
                                            name="filter"
                                            id="events"
                                            value="events"
                                            onChange={() => handleFilterButtonClick('events')}
                                            checked={activeFilterItem === 'events'}
                                        />
                                        <label htmlFor="events">Events</label>
                                    </li>
                                    <li className={activeFilterItem === 'publications' ? 'active' : ''}>
                                        <input
                                            type="radio"
                                            name="filter"
                                            id="publications"
                                            value="publications"
                                            onChange={() => handleFilterButtonClick('publications')}
                                            checked={activeFilterItem === 'publications'}
                                        />
                                        <label htmlFor="publications">Publications</label>
                                    </li>
                                </ul>
                            </div>
                        </div>
                        {/* Filter radio group END */}
                        <div className="comp-header_center__UgA6y">
                            <div className="comp-header_logo__zU5s9 full-header-logo">
                                <a
                                    className="font-white dark:font-black"
                                    target="_self"
                                    title="Homepage"
                                    itemProp="url"
                                    href="/"
                                >
                                    <img
                                        width="120"
                                        src="./logo_dark_hp.svg"
                                        alt="Interprt Logo"
                                    />
                                </a>
                            </div>
                        </div>
                        <div className="comp-header_right__MWw1l">
                            <div className="comp-header_menu_container__6DN0_ hide-on-responsive">
                                {/* Navigation menu can go here if needed */}
                            </div>
                            <div className="header-hamburger_burger_holder__tOCQw">
                                <span className="header-hamburger_line__jOEz4"></span>
                                <span className="header-hamburger_line__jOEz4"></span>
                                <span className="header-hamburger_line__jOEz4"></span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div className="header-flyout-menu_flyout_menu_container__tcC_d menu-area">
                <div className="header-push"></div>
                <div className="container">
                    <div className="row g-4">
                        <div className="col-md-6">
                            <div className="header-flyout-menu_left__vt8mh">
                                <nav>
                                    <ul className="main-menu">
                                        {[
                                            { label: 'About', href: '/about' },
                                            { label: 'Ecocide Study', href: '/ecocide' },
                                        ].map((item, idx) => (
                                            <li className="menu-item" key={idx}>
                                                <div className="header-flyout-menu_menu_item_container__xhVZl">
                                                    <Link className="fs-title-5" target="_self" to={item.href} itemProp="url">
                                                        {item.label}
                                                    </Link>
                                                </div>
                                            </li>
                                        ))}
                                    </ul>
                                </nav>

                            </div>
                        </div>

                        <div className="col-md-6">
                            <div className="header-flyout-menu_right___ZD7U">
                                <ul className="contact-info">
                                    <li className="fs-title-7">
                                        INTERPRT AS <br />
                                        Org.nr.: 932794012 <br />
                                        Postal address: <br />
                                        Vår Frue gate 8, 7013 Trondheim, Norway
                                    </li>
                                    <li className="fs-title-7">
                                        {/* <a href="tel:+4402077696757">+44 (0) 20 7769 6757</a>
                                        <br /> */}
                                        <a href="mailto:<EMAIL>"><EMAIL></a>
                                    </li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </header>
    );
};

export default Header;

